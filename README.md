# DocScribe - Medical Consultation Transcription System

A complete medical transcription solution with Flutter frontend and Python backend, featuring NVIDIA Riva ASR integration, speaker diarization, and professional PDF generation.

## 🏗️ Architecture Overview

This project follows a clean separation of concerns with a modern microservices architecture:

- **Flutter App (Client)**: Handles all UI/UX, audio capture, PDF display, and local storage
- **Python Backend (Server)**: Manages NVIDIA Riva ASR integration, speaker diarization, and transcript processing

## 📁 Project Structure

```
DocScribe/
├── flutter_app/                   # 📱 Flutter frontend application
│   ├── lib/
│   │   ├── main.dart              # App entry point
│   │   ├── screens/               # UI screens (Home, Recording, Review, etc.)
│   │   ├── widgets/               # Reusable components (AudioMeter, TranscriptPreview)
│   │   ├── services/              # API clients and business logic
│   │   ├── models/                # Data models (Patient, Consultation, Transcript)
│   │   ├── providers/             # State management (Provider pattern)
│   │   └── utils/                 # Helpers and constants
│   ├── assets/                    # Static assets (fonts, images, icons)
│   └── pubspec.yaml               # Flutter dependencies
│
├── backend/                       # 🐍 Python backend server
│   ├── app/
│   │   ├── main.py                # FastAPI application entry
│   │   ├── api/                   # REST API endpoints
│   │   │   ├── routers/           # Route handlers (consultation, audio, transcript, pdf)
│   │   │   └── schemas.py         # Pydantic models
│   │   ├── services/              # Business logic
│   │   │   ├── riva_client.py     # NVIDIA Riva ASR integration
│   │   │   └── pdf_generator.py   # Professional PDF generation
│   │   ├── core/                  # Configuration and utilities
│   │   │   ├── config.py          # Settings and environment variables
│   │   │   └── utils.py           # Helper functions
│   │   └── models/                # Database models (if needed)
│   ├── requirements.txt           # Python dependencies
│   ├── .env.example              # Environment configuration template
│   └── README.md                  # Backend setup instructions
│
├── doc_transcribe/                # 🗂️ Legacy implementation (for reference)
├── doc_transcribe_new/            # 🗂️ Previous iteration (for reference)
└── README.md                      # This file - Project overview
```

## ✨ Key Features

### 📱 Flutter Frontend
- ✅ **Patient Management**: Secure patient information entry and validation
- ✅ **Audio Recording**: High-quality recording with real-time visualization
- ✅ **Live Transcription**: Real-time transcript preview during recording
- ✅ **Professional Reports**: PDF generation and viewing capabilities
- ✅ **History Management**: Consultation history with search and filtering
- ✅ **Secure Storage**: Encrypted local data storage (HIPAA compliant)
- ✅ **Cross-Platform**: Native iOS and Android support
- ✅ **Offline Mode**: Local functionality when network unavailable

### 🐍 Python Backend
- ✅ **NVIDIA Riva Integration**: Canary-1B ASR model with gRPC streaming
- ✅ **Real-Time Processing**: Audio streaming and live transcription
- ✅ **Speaker Diarization**: Automatic doctor/patient voice separation
- ✅ **Transcript Cleaning**: AI-powered transcript processing and filtering
- ✅ **RESTful API**: Clean API design for Flutter communication
- ✅ **PDF Generation**: Server-side professional report generation
- ✅ **Scalable Architecture**: FastAPI with async/await support
- ✅ **Security**: JWT authentication and encrypted data handling

## 🔧 NVIDIA Riva Configuration

| Setting | Value |
|---------|-------|
| **Server** | `grpc.nvcf.nvidia.com:443` |
| **Function ID** | `ee8dc628-76de-4acc-8595-1836e7e857bd` |
| **Model** | Canary-1B ASR with speaker diarization |
| **Languages** | 25+ supported (English, Spanish, Hindi, etc.) |
| **Audio Format** | 16kHz, 16-bit, mono WAV |
| **API Key** | Configured in backend `.env` file |

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ and pip
- Flutter 3.0+ and Dart SDK
- NVIDIA Riva API key ([Get one here](https://catalog.ngc.nvidia.com/))

### 1. Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your NVIDIA Riva API key

# Start server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Flutter Setup
```bash
cd flutter_app

# Install dependencies
flutter pub get

# Generate model files
flutter packages pub run build_runner build

# Run on device/simulator
flutter run
```

### 3. Verify Setup
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## 🔌 API Endpoints

### Consultation Management
- `POST /api/v1/start_consultation` - Initialize new consultation session
- `GET /api/v1/consultations` - Retrieve consultation history with filtering
- `GET /api/v1/consultations/{id}` - Get specific consultation details
- `DELETE /api/v1/consultations/{id}` - Delete consultation record

### Audio Processing
- `POST /api/v1/audio_chunk` - Stream audio chunks for real-time transcription
- `POST /api/v1/finish_consultation` - Complete consultation and process final transcript

### Transcript Management
- `GET /api/v1/consultations/{id}/transcript` - Get consultation transcript with filtering
- `PUT /api/v1/consultations/{id}/transcript/{segment_id}` - Edit transcript segments
- `GET /api/v1/consultations/{id}/transcript/statistics` - Get transcript analytics

### PDF Generation
- `POST /api/v1/generate_pdf` - Generate professional PDF report
- `GET /api/v1/download_pdf/{consultation_id}` - Download PDF file
- `GET /api/v1/pdf_preview/{consultation_id}` - Preview PDF in browser

## 🔄 Data Flow

```mermaid
graph TD
    A[Patient Info Entry] --> B[Start Consultation]
    B --> C[Audio Recording]
    C --> D[Real-time Streaming]
    D --> E[NVIDIA Riva ASR]
    E --> F[Live Transcript]
    F --> G[Speaker Diarization]
    G --> H[Transcript Cleaning]
    H --> I[Review & Edit]
    I --> J[Generate PDF]
    J --> K[Save & Share]
```

1. **Patient Info**: Flutter collects and validates patient metadata
2. **Audio Capture**: High-quality recording with real-time level monitoring
3. **Streaming**: Audio chunks sent to Python backend via HTTP/gRPC
4. **Transcription**: Backend forwards to NVIDIA Riva ASR for processing
5. **Diarization**: AI-powered speaker identification (Doctor/Patient filtering)
6. **Processing**: Clean, timestamped transcript returned to Flutter
7. **PDF Generation**: Professional medical report with cover page and notes
8. **Storage**: Secure local storage with optional cloud backup

## 🔒 Security & Compliance

### HIPAA Compliance
- ✅ **Data Encryption**: AES-256 encryption for data at rest and in transit
- ✅ **Access Controls**: Role-based access with audit logging
- ✅ **Data Minimization**: Only necessary patient data collected
- ✅ **Secure Transmission**: HTTPS/TLS for all API communications
- ✅ **Audit Trails**: Comprehensive logging of all data access
- ✅ **Data Retention**: Configurable retention policies

### Technical Security
- JWT token authentication
- API rate limiting and throttling
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

## 🛠️ Development

### Tech Stack
- **Frontend**: Flutter 3.0+, Dart, Provider state management
- **Backend**: Python 3.8+, FastAPI, asyncio, SQLAlchemy
- **AI/ML**: NVIDIA Riva ASR, pyannote.audio for diarization
- **Storage**: SQLite (dev), PostgreSQL (prod), encrypted file storage
- **PDF**: ReportLab for professional report generation

### Development Workflow
1. **Setup**: Clone repository and configure environment
2. **Backend**: Start Python server with hot reload
3. **Frontend**: Launch Flutter app on device/simulator
4. **Testing**: Run unit and integration tests
5. **Build**: Create production builds for deployment

### Code Quality
- ESLint/Dart analyzer for code quality
- Automated testing with CI/CD
- Code coverage reporting
- Security vulnerability scanning

## 📦 Deployment

### Production Deployment
- **Backend**: Docker containers with Kubernetes orchestration
- **Frontend**: Native app stores (Google Play, App Store)
- **Database**: PostgreSQL with backup and replication
- **Monitoring**: Application performance monitoring and logging

### Environment Configuration
- Development, staging, and production environments
- Environment-specific configuration management
- Secrets management for API keys and certificates

## 📄 License

**Proprietary Software** - Medical transcription solution for healthcare professionals.

This software is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## 🆘 Support

### Technical Support
- 📧 Email: <EMAIL>
- 📞 Phone: +****************
- 🌐 Documentation: [docs.docscribe.com](https://docs.docscribe.com)
- 🐛 Bug Reports: [GitHub Issues](https://github.com/docscribe/issues)

### Resources
- [API Documentation](https://api.docscribe.com/docs)
- [Flutter Setup Guide](./flutter_app/README.md)
- [Backend Setup Guide](./backend/README.md)
- [NVIDIA Riva Documentation](https://docs.nvidia.com/deeplearning/riva/)

---

**Built with ❤️ for healthcare professionals worldwide**
