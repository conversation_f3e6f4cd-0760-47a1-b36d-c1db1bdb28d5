# Environment Configuration
ENVIRONMENT=development
HOST=0.0.0.0
PORT=8000

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000","http://localhost:8080"]

# NVIDIA Riva Configuration
RIVA_SERVER=grpc.nvcf.nvidia.com:443
RIVA_API_KEY=your_nvidia_riva_api_key_here
RIVA_FUNCTION_ID=ee8dc628-76de-4acc-8595-1836e7e857bd
RIVA_MODEL_NAME=canary-1b

# Audio Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_CHUNK_DURATION_MS=100
MAX_AUDIO_DURATION_MINUTES=60

# Speaker Diarization
ENABLE_SPEAKER_DIARIZATION=true
DIARIZATION_MODEL=pyannote/speaker-diarization@2.1
MIN_SPEAKER_CONFIDENCE=0.7

# Storage Paths
STORAGE_PATH=./storage
AUDIO_STORAGE_PATH=./storage/audio
PDF_STORAGE_PATH=./storage/pdfs

# Database
DATABASE_URL=sqlite:///./docscribe.db

# Security
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/docscribe.log

# PDF Generation
PDF_TEMPLATE_PATH=./templates
CLINIC_NAME=Medical Clinic
CLINIC_ADDRESS=123 Medical Center Dr, Healthcare City, HC 12345
CLINIC_PHONE=(*************
