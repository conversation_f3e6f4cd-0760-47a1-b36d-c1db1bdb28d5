# DocScribe Backend

Python backend server for DocScribe medical transcription application with NVIDIA Riva ASR integration.

## Features

- **NVIDIA Riva ASR Integration**: Real-time speech-to-text using Canary-1B model
- **Speaker Diarization**: Automatic identification of doctor vs patient voices
- **RESTful API**: Clean API endpoints for Flutter frontend communication
- **PDF Generation**: Server-side PDF report generation with ReportLab
- **Audio Processing**: Real-time audio streaming and batch processing
- **Secure Storage**: Encrypted storage for sensitive medical data

## Architecture

```
backend/
├── app/
│   ├── main.py                # FastAPI application entry point
│   ├── api/                   # REST API endpoints
│   │   ├── routers/           # API route handlers
│   │   └── schemas.py         # Pydantic models for request/response
│   ├── services/              # Business logic services
│   │   ├── riva_client.py     # NVIDIA Riva ASR client
│   │   └── pdf_generator.py   # PDF report generation
│   ├── core/                  # Core configuration and utilities
│   │   ├── config.py          # Application settings
│   │   └── utils.py           # Helper functions
│   └── models/                # Data models (if using database)
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
└── README.md                 # This file
```

## Quick Start

### 1. Environment Setup

```bash
# Clone the repository
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Configuration:**
- `RIVA_API_KEY`: Your NVIDIA Riva API key from NGC catalog
- `RIVA_SERVER`: NVIDIA Riva server endpoint (default: grpc.nvcf.nvidia.com:443)
- `SECRET_KEY`: Secret key for JWT tokens (generate a secure random string)

### 3. Run the Server

```bash
# Development mode (with auto-reload)
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

The server will be available at:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs (development only)
- Health Check: http://localhost:8000/health

## API Endpoints

### Consultation Management
- `POST /api/v1/start_consultation` - Start new consultation
- `GET /api/v1/consultations` - Get consultation history
- `GET /api/v1/consultations/{id}` - Get specific consultation
- `DELETE /api/v1/consultations/{id}` - Delete consultation

### Audio Processing
- `POST /api/v1/audio_chunk` - Send audio chunk for real-time transcription
- `POST /api/v1/finish_consultation` - Complete consultation and process transcript

### Transcript Management
- `GET /api/v1/consultations/{id}/transcript` - Get consultation transcript
- `GET /api/v1/consultations/{id}/transcript/formatted` - Get formatted transcript
- `PUT /api/v1/consultations/{id}/transcript/{segment_id}` - Update transcript segment

### PDF Generation
- `POST /api/v1/generate_pdf` - Generate PDF report
- `GET /api/v1/download_pdf/{consultation_id}` - Download PDF
- `GET /api/v1/pdf_preview/{consultation_id}` - Preview PDF

## NVIDIA Riva Integration

### Configuration
The backend integrates with NVIDIA Riva using the following configuration:

```python
# Default settings
RIVA_SERVER = "grpc.nvcf.nvidia.com:443"
RIVA_FUNCTION_ID = "ee8dc628-76de-4acc-8595-1836e7e857bd"  # Canary-1B ASR
RIVA_MODEL_NAME = "canary-1b"
```

### API Key Setup
1. Visit [NVIDIA NGC Catalog](https://catalog.ngc.nvidia.com/)
2. Sign up/login to your NVIDIA account
3. Navigate to Riva ASR models
4. Generate an API key
5. Add the key to your `.env` file

### Supported Features
- **Real-time Streaming**: Audio chunks processed in real-time
- **Batch Processing**: Complete audio files processed at once
- **Multi-language Support**: 25+ languages supported
- **High Accuracy**: Optimized for medical terminology
- **Speaker Diarization**: Automatic speaker identification

## Speaker Diarization

The backend includes speaker diarization to separate doctor and patient voices:

### Method A: Post-processing (Default)
- Uses pyannote.audio for speaker clustering
- Processes complete audio after recording
- Higher accuracy but slight delay

### Method B: Real-time (Optional)
- Processes audio chunks in real-time
- Faster response but lower accuracy
- Requires pre-recorded voice samples

### Configuration
```python
ENABLE_SPEAKER_DIARIZATION = True
DIARIZATION_MODEL = "pyannote/speaker-diarization@2.1"
MIN_SPEAKER_CONFIDENCE = 0.7
```

## PDF Generation

Professional PDF reports are generated using ReportLab:

### Features
- **Cover Page**: Patient info, consultation details
- **Transcript Section**: Timestamped, speaker-labeled conversation
- **Notes Section**: Doctor's notes and treatment plans
- **Professional Formatting**: Medical-grade report layout

### Customization
Edit `app/services/pdf_generator.py` to customize:
- Report layout and styling
- Clinic branding and logos
- Additional sections or fields

## Data Storage

### File Storage
- **Audio Files**: Stored in `./storage/audio/`
- **PDF Reports**: Stored in `./storage/pdfs/`
- **Logs**: Stored in `./logs/`

### Database (Optional)
- Default: In-memory storage for development
- Production: Configure SQLite/PostgreSQL in `DATABASE_URL`

### Security
- All patient data encrypted at rest
- HTTPS/TLS for API communication
- Configurable data retention policies

## Development

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest
```

### Code Quality
```bash
# Format code
black app/

# Lint code
flake8 app/

# Type checking
mypy app/
```

### Docker Deployment
```bash
# Build image
docker build -t docscribe-backend .

# Run container
docker run -p 8000:8000 --env-file .env docscribe-backend
```

## Monitoring

### Health Checks
- `GET /health` - Basic health status
- `GET /` - API information

### Logging
- Structured logging with timestamps
- Configurable log levels
- File and console output

### Metrics (Future)
- Request/response times
- Transcription accuracy
- Error rates

## Security Considerations

### HIPAA Compliance
- Encrypted data storage
- Audit logging
- Access controls
- Data retention policies

### API Security
- JWT token authentication
- Rate limiting
- Input validation
- CORS configuration

## Troubleshooting

### Common Issues

1. **NVIDIA Riva Connection Failed**
   - Check API key validity
   - Verify network connectivity
   - Confirm server endpoint

2. **Audio Processing Errors**
   - Validate audio format (16kHz, 16-bit, mono)
   - Check file size limits
   - Verify codec support

3. **PDF Generation Failed**
   - Check storage permissions
   - Verify ReportLab installation
   - Review template configuration

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
uvicorn app.main:app --reload --log-level debug
```

## Support

For technical support:
1. Check the logs in `./logs/docscribe.log`
2. Review API documentation at `/docs`
3. Verify environment configuration
4. Contact development team

## License

Proprietary software for medical transcription applications.
