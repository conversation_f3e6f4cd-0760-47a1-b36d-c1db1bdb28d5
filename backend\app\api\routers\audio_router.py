import asyncio
from datetime import datetime
from typing import Dict, List
from fastapi import APIRouter, HTTPException, File, UploadFile, Header, Depends
import logging

from ...api.schemas import AudioChunkResponse, TranscriptSegment, SpeakerType, ConsultationStatus
from ...services.riva_client import RivaASRClient
from ...services.diarization_service import SpeakerDiarizationService
from ...core.utils import validate_audio_format
from .consultation_router import get_consultation_by_id, update_consultation

logger = logging.getLogger(__name__)
router = APIRouter()

# In-memory storage for active audio streams
active_streams: Dict[str, List[bytes]] = {}

@router.post("/audio_chunk", response_model=AudioChunkResponse)
async def process_audio_chunk(
    audio: UploadFile = File(...),
    consultation_id: str = Header(..., alias="X-Consultation-ID")
):
    """
    Process audio chunk for real-time transcription
    
    Receives audio chunks from Flutter app and forwards them to NVIDIA Riva
    for real-time speech-to-text processing.
    """
    try:
        # Get consultation
        consultation = get_consultation_by_id(consultation_id)
        
        # Update consultation status to recording if not already
        if consultation.status != ConsultationStatus.RECORDING:
            consultation.status = ConsultationStatus.RECORDING
            update_consultation(consultation)
        
        # Read audio data
        audio_data = await audio.read()
        
        # Validate audio format
        if not validate_audio_format(audio_data):
            logger.warning(f"Invalid audio format received for consultation {consultation_id}")
            # Continue processing anyway for demo purposes
        
        # Initialize audio stream for this consultation if not exists
        if consultation_id not in active_streams:
            active_streams[consultation_id] = []
        
        # Add chunk to stream
        active_streams[consultation_id].append(audio_data)
        
        logger.debug(f"Received audio chunk for consultation {consultation_id}: {len(audio_data)} bytes")
        
        # Process with NVIDIA Riva (mock implementation)
        async with RivaASRClient() as riva_client:
            # For real-time processing, we would stream this chunk to Riva
            # For now, we'll generate mock responses
            
            chunk_count = len(active_streams[consultation_id])
            
            # Generate mock partial transcript every few chunks
            partial_transcript = None
            final_segments = []
            
            if chunk_count % 5 == 0:  # Every 5 chunks
                partial_transcript = _generate_mock_partial_text(chunk_count)
            
            if chunk_count % 20 == 0:  # Every 20 chunks, generate final segment
                final_segment = _generate_mock_final_segment(chunk_count, consultation_id)
                final_segments.append(final_segment)
                
                # Add to consultation transcript
                consultation.transcript.append(final_segment)
                update_consultation(consultation)
        
        response = AudioChunkResponse(
            status="streaming",
            partial_transcript=partial_transcript,
            final_segments=final_segments,
            transcript_id=consultation_id
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing audio chunk for consultation {consultation_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process audio chunk: {str(e)}")

@router.post("/finish_consultation")
async def finish_consultation(
    consultation_id: str = Header(..., alias="X-Consultation-ID")
):
    """
    Finish consultation and process complete transcript
    
    Signals the end of audio streaming and triggers final processing
    including speaker diarization and transcript cleaning.
    """
    try:
        # Get consultation
        consultation = get_consultation_by_id(consultation_id)
        
        if consultation.status != ConsultationStatus.RECORDING:
            raise HTTPException(status_code=400, detail="Consultation is not in recording state")
        
        # Update status to processing
        consultation.status = ConsultationStatus.PROCESSING
        update_consultation(consultation)
        
        logger.info(f"Finishing consultation {consultation_id}")
        
        # Get accumulated audio data
        audio_chunks = active_streams.get(consultation_id, [])
        if not audio_chunks:
            raise HTTPException(status_code=400, detail="No audio data found for consultation")
        
        # Combine all audio chunks
        complete_audio = b''.join(audio_chunks)
        
        # Process complete audio with NVIDIA Riva for final transcript
        async with RivaASRClient() as riva_client:
            # Get complete transcript from Riva
            riva_results = await riva_client.batch_recognize(complete_audio)
            
            # Convert Riva results to transcript segments
            raw_segments = []
            for i, result in enumerate(riva_results):
                segment = TranscriptSegment(
                    id=f"{consultation_id}_segment_{i}",
                    text=result.text,
                    speaker=SpeakerType.OTHER,  # Will be updated by diarization
                    timestamp=datetime.utcnow(),
                    start_time=result.start_time,
                    end_time=result.end_time,
                    confidence=result.confidence,
                    is_final=result.is_final
                )
                raw_segments.append(segment)
        
        # Perform speaker diarization and filtering
        clean_segments = await _perform_speaker_diarization(complete_audio, raw_segments)
        
        # Update consultation with clean transcript
        consultation.transcript = clean_segments
        consultation.status = ConsultationStatus.COMPLETED
        consultation.completed_at = datetime.utcnow()
        consultation.duration = clean_segments[-1].end_time if clean_segments else 0.0
        
        update_consultation(consultation)
        
        # Clean up audio stream
        if consultation_id in active_streams:
            del active_streams[consultation_id]
        
        logger.info(f"Consultation {consultation_id} completed with {len(clean_segments)} segments")
        
        return {
            "clean_transcript": clean_segments
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finishing consultation {consultation_id}: {e}")
        
        # Update consultation status to error
        try:
            consultation = get_consultation_by_id(consultation_id)
            consultation.status = ConsultationStatus.ERROR
            consultation.error_message = str(e)
            update_consultation(consultation)
        except:
            pass
        
        raise HTTPException(status_code=500, detail=f"Failed to finish consultation: {str(e)}")

async def _perform_speaker_diarization(
    audio_data: bytes,
    raw_segments: List[TranscriptSegment]
) -> List[TranscriptSegment]:
    """
    Perform speaker diarization and filter out non-doctor/patient voices
    """
    try:
        logger.info("Performing speaker diarization...")

        # Initialize diarization service
        diarization_service = SpeakerDiarizationService()
        await diarization_service.initialize()

        # Perform diarization
        clean_segments = await diarization_service.perform_diarization(
            audio_data,
            raw_segments
        )

        logger.info(f"Speaker diarization completed: {len(clean_segments)} segments")
        return clean_segments

    except Exception as e:
        logger.error(f"Error in speaker diarization: {e}")
        # Return original segments if diarization fails
        return raw_segments

def _generate_mock_partial_text(chunk_count: int) -> str:
    """Generate mock partial transcript text"""
    partial_phrases = [
        "Good morning",
        "How are you feeling",
        "Can you describe",
        "When did this",
        "On a scale of",
        "Have you experienced",
        "Let me examine",
        "I think we should",
    ]
    return partial_phrases[chunk_count % len(partial_phrases)]

def _generate_mock_final_segment(chunk_count: int, consultation_id: str) -> TranscriptSegment:
    """Generate mock final transcript segment"""
    final_phrases = [
        "Good morning, how are you feeling today?",
        "I've been having some chest pain.",
        "Can you describe the pain for me?",
        "It's a dull ache in the center of my chest.",
        "When did this pain start?",
        "It started yesterday evening.",
        "On a scale of one to ten, how severe is it?",
        "I'd say about a six or seven.",
    ]
    
    segment_index = (chunk_count // 20) - 1
    text = final_phrases[segment_index % len(final_phrases)]
    
    # Alternate between doctor and patient
    speaker = SpeakerType.DOCTOR if segment_index % 2 == 0 else SpeakerType.PATIENT
    
    start_time = segment_index * 4.0
    end_time = start_time + 3.5
    
    return TranscriptSegment(
        id=f"{consultation_id}_segment_{segment_index}",
        text=text,
        speaker=speaker,
        timestamp=datetime.utcnow(),
        start_time=start_time,
        end_time=end_time,
        confidence=0.95,
        is_final=True
    )
