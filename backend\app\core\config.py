import os
from datetime import datetime
from typing import List

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

from pydantic import Field

class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "DocScribe Backend"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    
    # NVIDIA Riva Configuration
    RIVA_SERVER: str = Field(default="grpc.nvcf.nvidia.com:443", env="RIVA_SERVER")
    RIVA_API_KEY: str = Field(default="your_nvidia_riva_api_key_here", env="RIVA_API_KEY")
    RIVA_FUNCTION_ID: str = Field(
        default="ee8dc628-76de-4acc-8595-1836e7e857bd",
        env="RIVA_FUNCTION_ID"
    )
    RIVA_MODEL_NAME: str = Field(default="canary-1b", env="RIVA_MODEL_NAME")
    
    # Audio Configuration
    AUDIO_SAMPLE_RATE: int = Field(default=16000, env="AUDIO_SAMPLE_RATE")
    AUDIO_CHANNELS: int = Field(default=1, env="AUDIO_CHANNELS")
    AUDIO_CHUNK_DURATION_MS: int = Field(default=100, env="AUDIO_CHUNK_DURATION_MS")
    MAX_AUDIO_DURATION_MINUTES: int = Field(default=60, env="MAX_AUDIO_DURATION_MINUTES")
    
    # Speaker Diarization
    ENABLE_SPEAKER_DIARIZATION: bool = Field(default=True, env="ENABLE_SPEAKER_DIARIZATION")
    DIARIZATION_MODEL: str = Field(
        default="pyannote/speaker-diarization@2.1",
        env="DIARIZATION_MODEL"
    )
    MIN_SPEAKER_CONFIDENCE: float = Field(default=0.7, env="MIN_SPEAKER_CONFIDENCE")
    
    # Storage
    STORAGE_PATH: str = Field(default="./storage", env="STORAGE_PATH")
    AUDIO_STORAGE_PATH: str = Field(default="./storage/audio", env="AUDIO_STORAGE_PATH")
    PDF_STORAGE_PATH: str = Field(default="./storage/pdfs", env="PDF_STORAGE_PATH")
    
    # Database
    DATABASE_URL: str = Field(
        default="sqlite:///./docscribe.db",
        env="DATABASE_URL"
    )
    
    # Security
    SECRET_KEY: str = Field(env="SECRET_KEY", default="your-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="./logs/docscribe.log", env="LOG_FILE")
    
    # PDF Generation
    PDF_TEMPLATE_PATH: str = Field(default="./templates", env="PDF_TEMPLATE_PATH")
    CLINIC_NAME: str = Field(default="Medical Clinic", env="CLINIC_NAME")
    CLINIC_ADDRESS: str = Field(default="", env="CLINIC_ADDRESS")
    CLINIC_PHONE: str = Field(default="", env="CLINIC_PHONE")
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    def get_current_timestamp(self) -> str:
        """Get current timestamp as ISO string"""
        return datetime.utcnow().isoformat()

    def ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            self.STORAGE_PATH,
            self.AUDIO_STORAGE_PATH,
            self.PDF_STORAGE_PATH,
            os.path.dirname(self.LOG_FILE),
            self.PDF_TEMPLATE_PATH,
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)

# Create global settings instance
settings = Settings()

# Ensure directories exist on import
settings.ensure_directories()
