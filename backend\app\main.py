from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from .core.config import settings
from .api.routers import consultation_router, audio_router, transcript_router, pdf_router
from .core.utils import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting DocScribe Backend Server")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"NVIDIA Riva Server: {settings.RIVA_SERVER}")

    # Initialize services
    try:
        from .services.diarization_service import SpeakerDiarizationService
        diarization_service = SpeakerDiarizationService()
        await diarization_service.initialize()
        logger.info("Services initialized successfully")
    except Exception as e:
        logger.warning(f"Some services failed to initialize: {e}")

    yield

    logger.info("Shutting down DocScribe Backend Server")

# Create FastAPI application
app = FastAPI(
    title="DocScribe Backend API",
    description="Medical consultation transcription backend with NVIDIA Riva ASR integration",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan,
)

# CORS middleware for Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(
    consultation_router.router,
    prefix="/api/v1",
    tags=["consultations"]
)

app.include_router(
    audio_router.router,
    prefix="/api/v1",
    tags=["audio"]
)

app.include_router(
    transcript_router.router,
    prefix="/api/v1",
    tags=["transcripts"]
)

app.include_router(
    pdf_router.router,
    prefix="/api/v1",
    tags=["pdf"]
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "DocScribe Backend API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs" if settings.ENVIRONMENT == "development" else "disabled"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": settings.get_current_timestamp(),
        "environment": settings.ENVIRONMENT
    }

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Global HTTP exception handler"""
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
