import asyncio
import logging
import tempfile
import os
from typing import List, Dict, <PERSON><PERSON>
import numpy as np

from ..core.config import settings
from ..api.schemas import TranscriptSegment, SpeakerType, DiarizationResult

logger = logging.getLogger(__name__)

class SpeakerDiarizationService:
    """Service for speaker diarization and voice separation"""
    
    def __init__(self):
        self.model_loaded = False
        self.diarization_pipeline = None
        
    async def initialize(self):
        """Initialize the diarization model"""
        try:
            if not settings.ENABLE_SPEAKER_DIARIZATION:
                logger.info("Speaker diarization is disabled")
                return
                
            # Try to load pyannote.audio model
            try:
                from pyannote.audio import Pipeline
                self.diarization_pipeline = Pipeline.from_pretrained(
                    settings.DIARIZATION_MODEL,
                    use_auth_token=None  # Add HuggingFace token if needed
                )
                self.model_loaded = True
                logger.info(f"Loaded diarization model: {settings.DIARIZATION_MODEL}")
            except Exception as e:
                logger.warning(f"Failed to load pyannote model: {e}")
                logger.info("Using fallback diarization method")
                
        except Exception as e:
            logger.error(f"Error initializing diarization service: {e}")
    
    async def perform_diarization(
        self, 
        audio_data: bytes, 
        transcript_segments: List[TranscriptSegment]
    ) -> List[TranscriptSegment]:
        """
        Perform speaker diarization on audio and map to transcript segments
        
        Args:
            audio_data: Raw audio data
            transcript_segments: List of transcript segments from ASR
            
        Returns:
            List of transcript segments with speaker labels
        """
        try:
            if not settings.ENABLE_SPEAKER_DIARIZATION:
                return self._assign_alternating_speakers(transcript_segments)
            
            if self.model_loaded and self.diarization_pipeline:
                return await self._pyannote_diarization(audio_data, transcript_segments)
            else:
                return await self._fallback_diarization(audio_data, transcript_segments)
                
        except Exception as e:
            logger.error(f"Error in speaker diarization: {e}")
            return self._assign_alternating_speakers(transcript_segments)
    
    async def _pyannote_diarization(
        self, 
        audio_data: bytes, 
        transcript_segments: List[TranscriptSegment]
    ) -> List[TranscriptSegment]:
        """Perform diarization using pyannote.audio"""
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            try:
                # Run diarization
                diarization = self.diarization_pipeline(temp_path)
                
                # Convert diarization results to speaker mapping
                speaker_segments = []
                for turn, _, speaker in diarization.itertracks(yield_label=True):
                    speaker_segments.append({
                        'start': turn.start,
                        'end': turn.end,
                        'speaker': speaker
                    })
                
                # Map speakers to Doctor/Patient
                speaker_mapping = self._map_speakers_to_roles(speaker_segments)
                
                # Apply speaker labels to transcript segments
                labeled_segments = self._apply_speaker_labels(
                    transcript_segments, 
                    speaker_segments, 
                    speaker_mapping
                )
                
                logger.info(f"Pyannote diarization completed: {len(speaker_segments)} speaker segments")
                return labeled_segments
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logger.error(f"Error in pyannote diarization: {e}")
            return await self._fallback_diarization(audio_data, transcript_segments)
    
    async def _fallback_diarization(
        self, 
        audio_data: bytes, 
        transcript_segments: List[TranscriptSegment]
    ) -> List[TranscriptSegment]:
        """Fallback diarization using simple heuristics"""
        try:
            logger.info("Using fallback diarization method")
            
            # Simple approach: analyze audio energy and timing patterns
            speaker_changes = self._detect_speaker_changes(transcript_segments)
            
            # Apply speaker labels based on detected changes
            labeled_segments = []
            current_speaker = SpeakerType.DOCTOR  # Start with doctor
            
            for i, segment in enumerate(transcript_segments):
                if i in speaker_changes:
                    # Switch speaker
                    current_speaker = (
                        SpeakerType.PATIENT if current_speaker == SpeakerType.DOCTOR 
                        else SpeakerType.DOCTOR
                    )
                
                labeled_segment = TranscriptSegment(
                    id=segment.id,
                    text=segment.text,
                    speaker=current_speaker,
                    timestamp=segment.timestamp,
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    confidence=segment.confidence,
                    is_final=segment.is_final
                )
                labeled_segments.append(labeled_segment)
            
            return labeled_segments
            
        except Exception as e:
            logger.error(f"Error in fallback diarization: {e}")
            return self._assign_alternating_speakers(transcript_segments)
    
    def _detect_speaker_changes(self, transcript_segments: List[TranscriptSegment]) -> List[int]:
        """Detect potential speaker changes based on timing and content"""
        speaker_changes = []
        
        for i in range(1, len(transcript_segments)):
            current = transcript_segments[i]
            previous = transcript_segments[i-1]
            
            # Check for timing gaps (potential speaker change)
            gap = current.start_time - previous.end_time
            if gap > 1.0:  # 1 second gap
                speaker_changes.append(i)
                continue
            
            # Check for question/answer patterns
            if self._is_question(previous.text) and self._is_answer(current.text):
                speaker_changes.append(i)
                continue
            
            # Check for greeting patterns
            if self._is_greeting(current.text):
                speaker_changes.append(i)
        
        return speaker_changes
    
    def _is_question(self, text: str) -> bool:
        """Check if text appears to be a question"""
        question_words = ['what', 'how', 'when', 'where', 'why', 'who', 'can', 'do', 'are', 'is']
        text_lower = text.lower().strip()
        
        return (
            text_lower.endswith('?') or
            any(text_lower.startswith(word) for word in question_words)
        )
    
    def _is_answer(self, text: str) -> bool:
        """Check if text appears to be an answer"""
        answer_starters = ['yes', 'no', 'i', 'my', 'it', 'the', 'about', 'since']
        text_lower = text.lower().strip()
        
        return any(text_lower.startswith(word) for word in answer_starters)
    
    def _is_greeting(self, text: str) -> bool:
        """Check if text appears to be a greeting"""
        greetings = ['hello', 'hi', 'good morning', 'good afternoon', 'good evening']
        text_lower = text.lower().strip()
        
        return any(greeting in text_lower for greeting in greetings)
    
    def _map_speakers_to_roles(self, speaker_segments: List[Dict]) -> Dict[str, SpeakerType]:
        """Map detected speakers to Doctor/Patient roles"""
        # Count speaking time for each speaker
        speaker_times = {}
        for segment in speaker_segments:
            speaker = segment['speaker']
            duration = segment['end'] - segment['start']
            speaker_times[speaker] = speaker_times.get(speaker, 0) + duration
        
        # Sort speakers by speaking time
        sorted_speakers = sorted(speaker_times.items(), key=lambda x: x[1], reverse=True)
        
        # Assign roles: most active speaker is usually the doctor
        speaker_mapping = {}
        for i, (speaker, _) in enumerate(sorted_speakers):
            if i == 0:
                speaker_mapping[speaker] = SpeakerType.DOCTOR
            elif i == 1:
                speaker_mapping[speaker] = SpeakerType.PATIENT
            else:
                speaker_mapping[speaker] = SpeakerType.OTHER
        
        return speaker_mapping
    
    def _apply_speaker_labels(
        self, 
        transcript_segments: List[TranscriptSegment],
        speaker_segments: List[Dict],
        speaker_mapping: Dict[str, SpeakerType]
    ) -> List[TranscriptSegment]:
        """Apply speaker labels to transcript segments"""
        labeled_segments = []
        
        for segment in transcript_segments:
            # Find overlapping speaker segment
            speaker_type = SpeakerType.OTHER
            
            for speaker_seg in speaker_segments:
                # Check for overlap
                if (segment.start_time < speaker_seg['end'] and 
                    segment.end_time > speaker_seg['start']):
                    speaker_id = speaker_seg['speaker']
                    speaker_type = speaker_mapping.get(speaker_id, SpeakerType.OTHER)
                    break
            
            # Filter out "other" speakers if configured
            if speaker_type == SpeakerType.OTHER:
                continue
            
            labeled_segment = TranscriptSegment(
                id=segment.id,
                text=segment.text,
                speaker=speaker_type,
                timestamp=segment.timestamp,
                start_time=segment.start_time,
                end_time=segment.end_time,
                confidence=segment.confidence,
                is_final=segment.is_final
            )
            labeled_segments.append(labeled_segment)
        
        return labeled_segments
    
    def _assign_alternating_speakers(self, transcript_segments: List[TranscriptSegment]) -> List[TranscriptSegment]:
        """Simple alternating speaker assignment as fallback"""
        labeled_segments = []
        current_speaker = SpeakerType.DOCTOR
        
        for i, segment in enumerate(transcript_segments):
            # Switch speaker every few segments
            if i > 0 and i % 2 == 0:
                current_speaker = (
                    SpeakerType.PATIENT if current_speaker == SpeakerType.DOCTOR 
                    else SpeakerType.DOCTOR
                )
            
            labeled_segment = TranscriptSegment(
                id=segment.id,
                text=segment.text,
                speaker=current_speaker,
                timestamp=segment.timestamp,
                start_time=segment.start_time,
                end_time=segment.end_time,
                confidence=segment.confidence,
                is_final=segment.is_final
            )
            labeled_segments.append(labeled_segment)
        
        return labeled_segments
