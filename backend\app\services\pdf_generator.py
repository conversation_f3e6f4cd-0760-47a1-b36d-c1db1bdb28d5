import io
from datetime import datetime
from typing import Optional
from fpdf import FPDF
import logging

from ..api.schemas import Consultation, SpeakerType
from ..core.config import settings
from ..core.utils import format_duration

logger = logging.getLogger(__name__)

class PDFGenerator:
    """PDF generator for consultation reports using FPDF"""

    def __init__(self):
        self.pdf = None
    
    async def generate_consultation_report(
        self,
        consultation: Consultation,
        doctor_notes: Optional[str] = None
    ) -> bytes:
        """
        Generate complete consultation report PDF

        Args:
            consultation: Consultation object with patient info and transcript
            doctor_notes: Optional doctor's notes to include

        Returns:
            PDF content as bytes
        """
        try:
            logger.info(f"Generating PDF report for consultation {consultation.id}")

            # Create PDF
            self.pdf = FPDF()
            self.pdf.add_page()

            # Build document content
            self._build_cover_page(consultation)

            # Add new page for transcript
            if consultation.transcript:
                self.pdf.add_page()
                self._build_transcript_section(consultation)

            # Add notes page if available
            if doctor_notes or consultation.doctor_notes:
                self.pdf.add_page()
                self._build_notes_section(doctor_notes or consultation.doctor_notes)

            # Get PDF bytes
            pdf_bytes = self.pdf.output(dest='S').encode('latin-1')

            logger.info(f"PDF report generated successfully: {len(pdf_bytes)} bytes")
            return pdf_bytes

        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            raise
    
    def _build_cover_page(self, consultation: Consultation):
        """Build cover page content"""
        # Title
        self.pdf.set_font('Arial', 'B', 20)
        self.pdf.cell(0, 15, 'Medical Consultation Report', 0, 1, 'C')
        self.pdf.ln(10)

        # Clinic information
        self.pdf.set_font('Arial', 'B', 14)
        if hasattr(settings, 'CLINIC_NAME') and settings.CLINIC_NAME:
            self.pdf.cell(0, 10, settings.CLINIC_NAME, 0, 1, 'C')

        self.pdf.set_font('Arial', '', 10)
        if hasattr(settings, 'CLINIC_ADDRESS') and settings.CLINIC_ADDRESS:
            self.pdf.cell(0, 8, settings.CLINIC_ADDRESS, 0, 1, 'C')

        if hasattr(settings, 'CLINIC_PHONE') and settings.CLINIC_PHONE:
            self.pdf.cell(0, 8, f"Phone: {settings.CLINIC_PHONE}", 0, 1, 'C')

        self.pdf.ln(15)

        # Patient Information
        self.pdf.set_font('Arial', 'B', 12)
        self.pdf.cell(0, 10, 'Patient Information', 0, 1)
        self.pdf.set_font('Arial', '', 10)

        self.pdf.cell(40, 8, 'Name:', 0, 0)
        self.pdf.cell(0, 8, consultation.patient.name, 0, 1)

        self.pdf.cell(40, 8, 'Age:', 0, 0)
        self.pdf.cell(0, 8, f"{consultation.patient.age} years", 0, 1)

        self.pdf.cell(40, 8, 'Gender:', 0, 0)
        self.pdf.cell(0, 8, consultation.patient.gender, 0, 1)

        if consultation.patient.patient_id:
            self.pdf.cell(40, 8, 'Patient ID:', 0, 0)
            self.pdf.cell(0, 8, consultation.patient.patient_id, 0, 1)

        if consultation.patient.visit_reason:
            self.pdf.cell(40, 8, 'Reason for Visit:', 0, 0)
            self.pdf.cell(0, 8, consultation.patient.visit_reason, 0, 1)

        self.pdf.ln(10)

        # Consultation Information
        self.pdf.set_font('Arial', 'B', 12)
        self.pdf.cell(0, 10, 'Consultation Details', 0, 1)
        self.pdf.set_font('Arial', '', 10)

        self.pdf.cell(40, 8, 'Date:', 0, 0)
        self.pdf.cell(0, 8, consultation.created_at.strftime('%B %d, %Y'), 0, 1)

        self.pdf.cell(40, 8, 'Time:', 0, 0)
        self.pdf.cell(0, 8, consultation.created_at.strftime('%I:%M %p'), 0, 1)

        self.pdf.cell(40, 8, 'Doctor:', 0, 0)
        self.pdf.cell(0, 8, consultation.doctor_name or 'Not specified', 0, 1)

        if consultation.duration:
            self.pdf.cell(40, 8, 'Duration:', 0, 0)
            self.pdf.cell(0, 8, format_duration(consultation.duration), 0, 1)
    
    def _build_transcript_section(self, consultation: Consultation):
        """Build transcript section content"""
        # Section header
        self.pdf.set_font('Arial', 'B', 16)
        self.pdf.cell(0, 12, 'Consultation Transcript', 0, 1)
        self.pdf.ln(5)

        if not consultation.transcript:
            self.pdf.set_font('Arial', '', 10)
            self.pdf.cell(0, 8, 'No transcript available.', 0, 1)
            return

        # Transcript statistics
        total_words = len(' '.join([s.text for s in consultation.transcript]).split())
        doctor_words = len(' '.join([s.text for s in consultation.transcript if s.speaker == SpeakerType.DOCTOR]).split())
        patient_words = len(' '.join([s.text for s in consultation.transcript if s.speaker == SpeakerType.PATIENT]).split())

        self.pdf.set_font('Arial', '', 9)
        stats_text = f"Total words: {total_words} | Doctor: {doctor_words} | Patient: {patient_words}"
        self.pdf.cell(0, 6, stats_text, 0, 1)
        self.pdf.ln(5)

        # Transcript content
        for segment in consultation.transcript:
            # Timestamp and speaker
            timestamp = f"[{int(segment.start_time // 60):02d}:{int(segment.start_time % 60):02d}]"
            speaker_label = "Doctor" if segment.speaker == SpeakerType.DOCTOR else "Patient"

            # Speaker line
            self.pdf.set_font('Arial', 'B', 10)
            speaker_line = f"{timestamp} {speaker_label}:"
            self.pdf.cell(0, 6, speaker_line, 0, 1)

            # Transcript text
            self.pdf.set_font('Arial', '', 10)
            # Handle long text by splitting into multiple lines
            text_lines = self._split_text(segment.text, 80)
            for line in text_lines:
                self.pdf.cell(10, 5, '', 0, 0)  # Indent
                self.pdf.cell(0, 5, line, 0, 1)

            self.pdf.ln(2)
    
    def _build_notes_section(self, notes: str):
        """Build doctor's notes section"""
        # Section header
        self.pdf.set_font('Arial', 'B', 16)
        self.pdf.cell(0, 12, "Doctor's Notes & Impression", 0, 1)
        self.pdf.ln(5)

        # Notes content
        self.pdf.set_font('Arial', '', 10)

        # Split notes into paragraphs and handle long text
        note_paragraphs = notes.split('\n')
        for paragraph in note_paragraphs:
            if paragraph.strip():
                text_lines = self._split_text(paragraph.strip(), 90)
                for line in text_lines:
                    self.pdf.cell(0, 6, line, 0, 1)
                self.pdf.ln(2)

    def _split_text(self, text: str, max_chars: int) -> list:
        """Split text into lines that fit within the specified character limit"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            if len(current_line + " " + word) <= max_chars:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines if lines else [""]
