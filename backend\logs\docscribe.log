2025-06-01 23:59:03,738 - app.main - INFO - Starting DocScribe Backend Server
2025-06-01 23:59:03,738 - app.main - INFO - Environment: development
2025-06-01 23:59:03,739 - app.main - INFO - NVIDIA Riva Server: grpc.nvcf.nvidia.com:443
2025-06-01 23:59:03,741 - app.services.diarization_service - WARNING - Failed to load pyannote model: No module named 'pyannote'
2025-06-01 23:59:03,741 - app.services.diarization_service - INFO - Using fallback diarization method
2025-06-01 23:59:03,741 - app.main - INFO - Services initialized successfully
2025-06-01 23:59:03,743 - app.main - INFO - Shutting down DocScribe Backend Server
2025-06-02 00:02:07,941 - app.main - INFO - Starting DocScribe Backend Server
2025-06-02 00:02:07,941 - app.main - INFO - Environment: development
2025-06-02 00:02:07,942 - app.main - INFO - NVIDIA Riva Server: grpc.nvcf.nvidia.com:443
2025-06-02 00:02:07,945 - app.services.diarization_service - WARNING - Failed to load pyannote model: No module named 'pyannote'
2025-06-02 00:02:07,945 - app.services.diarization_service - INFO - Using fallback diarization method
2025-06-02 00:02:07,946 - app.main - INFO - Services initialized successfully
2025-06-02 00:02:56,205 - app.api.routers.consultation_router - INFO - Started new consultation: 4d3681fb-297f-4b8c-9681-d67af082ba47 for patient: Test Patient
