-- Merging decision tree log ---
application
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:11:5-57:19
MERGED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-19:19
MERGED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-19:19
MERGED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-35:19
MERGED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-35:19
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:13:5-14:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9c34a9b71ea0d57f574c4ebc71ed16a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9c34a9b71ea0d57f574c4ebc71ed16a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:requestLegacyExternalStorage
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
	android:icon
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
	android:name
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
provider#androidx.core.content.FileProvider
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
	android:authorities
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
	android:exported
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
manifest
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
MERGED from [:audio_waveforms] C:\python_programs\DocScribe\doc_transcribe\build\audio_waveforms\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_sound] C:\python_programs\DocScribe\doc_transcribe\build\flutter_sound\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\python_programs\DocScribe\doc_transcribe\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:permission_handler_android] C:\python_programs\DocScribe\doc_transcribe\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-21:12
MERGED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-37:12
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4fa9196a46da0fe30cf290869c0497\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\019f1354596c9157de919eeaf34898f6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.exoplayer:exoplayer:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\712cc0460a350b99c6781d064cc36c7c\transformed\jetified-exoplayer-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\056da2ce862f10370dc87214357930e4\transformed\jetified-exoplayer-dash-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\a41289502c4290a923f5e7e3fa0901a3\transformed\jetified-exoplayer-hls-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a8fc168525b4bf4403ba5484b5075f3\transformed\jetified-exoplayer-rtsp-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a3b68b2ddab113dc5c226985bdcba7d\transformed\jetified-exoplayer-smoothstreaming-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc4c0aa845d72fda0dd7b9b816c58c3c\transformed\jetified-exoplayer-ui-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d93a2c3f09faef6384fb2c8c6236ff\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8195be991b57db27e970c681866611c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf6ae328e54b92e9215264b71b8866bc\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdf28e1efaec1f3184a5c597f2146f91\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2ec7fb733a6381005fd6aa94972168b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bed24815a27ea775e9a934e8154f0fb6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71fb58f0bddf09641dfc956a06c8bca6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a2dbf4f96c6dbec5c3dc6707cb9de\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76e81b3835858943920f570981ec666\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a86d3ec1669f10da667b85e573f87715\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd7f33683ffd85cac249325c937f42f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e500f11e4d7a3db202b5363074afe5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b30e4dc937276f023b4a6e07a5156550\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6cc079d3a83268c2582a050621870aa2\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b2c08c0e87b93f85507a804fb3c19a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\67b33150bcd3b75425761549f47fdf53\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\20f90272ce32a018f9f967394d8a2a17\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa11fe827c549893c9e5c2fc6854bbf6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\db865cfc583742df8a08e9a06de1f57a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\10e3d3c57b390b9cf9961d7096409fa8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\d14b77f53b2fa9e10ab9dfde506edab1\transformed\jetified-exoplayer-datasource-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b3897ebf3830e4782b943b7fbe54ab5\transformed\jetified-exoplayer-database-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\38e54c6e216f9e0be39a1d0684d1524d\transformed\jetified-exoplayer-extractor-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\fad078ee22c5c1154ea3c7e04ccf36e5\transformed\jetified-exoplayer-decoder-2.17.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\e42e4e1de933a12bc9e8f10c6a27a719\transformed\jetified-exoplayer-common-2.17.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9c34a9b71ea0d57f574c4ebc71ed16a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4192350f885bf9c4e5d15192742773b1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc967194b5a773bfebf7021c347cc5cd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\34b683b690f10e6e11baa9a53be22c0b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9961c5ce45b8c69bb8f4ca386e222dbf\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\30f980438aad3643f9d80ee4b88a5829\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff647feb7b94dc3cf5bc95c8486b4a1e\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
	package
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:1-58:12
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:5-71
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:5-71
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
uses-sdk
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
MERGED from [:audio_waveforms] C:\python_programs\DocScribe\doc_transcribe\build\audio_waveforms\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:audio_waveforms] C:\python_programs\DocScribe\doc_transcribe\build\audio_waveforms\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_sound] C:\python_programs\DocScribe\doc_transcribe\build\flutter_sound\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_sound] C:\python_programs\DocScribe\doc_transcribe\build\flutter_sound\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] C:\python_programs\DocScribe\doc_transcribe\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] C:\python_programs\DocScribe\doc_transcribe\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] C:\python_programs\DocScribe\doc_transcribe\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] C:\python_programs\DocScribe\doc_transcribe\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4fa9196a46da0fe30cf290869c0497\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4fa9196a46da0fe30cf290869c0497\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\019f1354596c9157de919eeaf34898f6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\019f1354596c9157de919eeaf34898f6\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\712cc0460a350b99c6781d064cc36c7c\transformed\jetified-exoplayer-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\712cc0460a350b99c6781d064cc36c7c\transformed\jetified-exoplayer-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\056da2ce862f10370dc87214357930e4\transformed\jetified-exoplayer-dash-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\056da2ce862f10370dc87214357930e4\transformed\jetified-exoplayer-dash-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\a41289502c4290a923f5e7e3fa0901a3\transformed\jetified-exoplayer-hls-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\a41289502c4290a923f5e7e3fa0901a3\transformed\jetified-exoplayer-hls-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a8fc168525b4bf4403ba5484b5075f3\transformed\jetified-exoplayer-rtsp-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a8fc168525b4bf4403ba5484b5075f3\transformed\jetified-exoplayer-rtsp-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a3b68b2ddab113dc5c226985bdcba7d\transformed\jetified-exoplayer-smoothstreaming-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a3b68b2ddab113dc5c226985bdcba7d\transformed\jetified-exoplayer-smoothstreaming-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc4c0aa845d72fda0dd7b9b816c58c3c\transformed\jetified-exoplayer-ui-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc4c0aa845d72fda0dd7b9b816c58c3c\transformed\jetified-exoplayer-ui-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d93a2c3f09faef6384fb2c8c6236ff\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5d93a2c3f09faef6384fb2c8c6236ff\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8195be991b57db27e970c681866611c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8195be991b57db27e970c681866611c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf6ae328e54b92e9215264b71b8866bc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf6ae328e54b92e9215264b71b8866bc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdf28e1efaec1f3184a5c597f2146f91\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdf28e1efaec1f3184a5c597f2146f91\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2ec7fb733a6381005fd6aa94972168b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2ec7fb733a6381005fd6aa94972168b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bed24815a27ea775e9a934e8154f0fb6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bed24815a27ea775e9a934e8154f0fb6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71fb58f0bddf09641dfc956a06c8bca6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\71fb58f0bddf09641dfc956a06c8bca6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a2dbf4f96c6dbec5c3dc6707cb9de\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a2dbf4f96c6dbec5c3dc6707cb9de\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76e81b3835858943920f570981ec666\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76e81b3835858943920f570981ec666\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a86d3ec1669f10da667b85e573f87715\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a86d3ec1669f10da667b85e573f87715\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd7f33683ffd85cac249325c937f42f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bd7f33683ffd85cac249325c937f42f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e500f11e4d7a3db202b5363074afe5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e500f11e4d7a3db202b5363074afe5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b30e4dc937276f023b4a6e07a5156550\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b30e4dc937276f023b4a6e07a5156550\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6cc079d3a83268c2582a050621870aa2\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6cc079d3a83268c2582a050621870aa2\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b2c08c0e87b93f85507a804fb3c19a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b2c08c0e87b93f85507a804fb3c19a3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\67b33150bcd3b75425761549f47fdf53\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\67b33150bcd3b75425761549f47fdf53\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\20f90272ce32a018f9f967394d8a2a17\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\20f90272ce32a018f9f967394d8a2a17\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa11fe827c549893c9e5c2fc6854bbf6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa11fe827c549893c9e5c2fc6854bbf6\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\db865cfc583742df8a08e9a06de1f57a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\db865cfc583742df8a08e9a06de1f57a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\10e3d3c57b390b9cf9961d7096409fa8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\10e3d3c57b390b9cf9961d7096409fa8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\d14b77f53b2fa9e10ab9dfde506edab1\transformed\jetified-exoplayer-datasource-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\d14b77f53b2fa9e10ab9dfde506edab1\transformed\jetified-exoplayer-datasource-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b3897ebf3830e4782b943b7fbe54ab5\transformed\jetified-exoplayer-database-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b3897ebf3830e4782b943b7fbe54ab5\transformed\jetified-exoplayer-database-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\38e54c6e216f9e0be39a1d0684d1524d\transformed\jetified-exoplayer-extractor-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\38e54c6e216f9e0be39a1d0684d1524d\transformed\jetified-exoplayer-extractor-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\fad078ee22c5c1154ea3c7e04ccf36e5\transformed\jetified-exoplayer-decoder-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\fad078ee22c5c1154ea3c7e04ccf36e5\transformed\jetified-exoplayer-decoder-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\e42e4e1de933a12bc9e8f10c6a27a719\transformed\jetified-exoplayer-common-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\e42e4e1de933a12bc9e8f10c6a27a719\transformed\jetified-exoplayer-common-2.17.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9c34a9b71ea0d57f574c4ebc71ed16a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b9c34a9b71ea0d57f574c4ebc71ed16a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4192350f885bf9c4e5d15192742773b1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4192350f885bf9c4e5d15192742773b1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc967194b5a773bfebf7021c347cc5cd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc967194b5a773bfebf7021c347cc5cd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\34b683b690f10e6e11baa9a53be22c0b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\34b683b690f10e6e11baa9a53be22c0b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9961c5ce45b8c69bb8f4ca386e222dbf\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9961c5ce45b8c69bb8f4ca386e222dbf\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\30f980438aad3643f9d80ee4b88a5829\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\30f980438aad3643f9d80ee4b88a5829\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff647feb7b94dc3cf5bc95c8486b4a1e\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff647feb7b94dc3cf5bc95c8486b4a1e\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
activity#com.example.doc_transcribe.MainActivity
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
	android:launchMode
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
	android:hardwareAccelerated
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
	android:windowSoftInputMode
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
	android:exported
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
	android:configChanges
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
	android:theme
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
	android:resource
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
	android:autoVerify
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
action#android.intent.action.MAIN
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
meta-data#flutterEmbedding
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
	android:value
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
	android:resource
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
	android:name
		ADDED from C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
provider#net.nfet.flutter.printing.PrintFileProvider
ADDED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-18:20
	android:grantUriPermissions
		ADDED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
	android:authorities
		ADDED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
	android:exported
		ADDED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-23:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-47
	android:authorities
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-74
	android:exported
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
	android:name
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-34:20
	android:exported
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-33:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-65
	android:name
		ADDED from [:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:25-62
uses-permission#android.permission.BLUETOOTH
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
uses-permission#Manifest.permission.CAPTURE_AUDIO_OUTPUT
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\e42e4e1de933a12bc9e8f10c6a27a719\transformed\jetified-exoplayer-common-2.17.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\e42e4e1de933a12bc9e8f10c6a27a719\transformed\jetified-exoplayer-common-2.17.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:22-76
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\43980c3f7213c92c8646e00d332d5bd5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
