# DocScribe Flutter App

Cross-platform mobile application for medical consultation transcription with NVIDIA Riva ASR integration.

## Features

- **Patient Information Management**: Secure patient data entry and storage
- **High-Quality Audio Recording**: Optimized for medical consultations
- **Real-Time Transcription**: Live speech-to-text with NVIDIA Riva
- **Speaker Identification**: Automatic doctor/patient voice separation
- **Professional PDF Reports**: Medical-grade consultation reports
- **Consultation History**: Secure local storage and management
- **Cross-Platform**: iOS and Android support

## Architecture

```
flutter_app/
├── lib/
│   ├── main.dart              # Application entry point
│   ├── screens/               # UI screens
│   │   ├── home_screen.dart
│   │   ├── patient_info_screen.dart
│   │   ├── recording_screen.dart
│   │   ├── review_transcript_screen.dart
│   │   ├── history_screen.dart
│   │   └── settings_screen.dart
│   ├── widgets/               # Reusable UI components
│   │   ├── audio_level_meter.dart
│   │   └── live_transcript_preview.dart
│   ├── services/              # Business logic and API clients
│   │   ├── api_service.dart
│   │   └── audio_service.dart
│   ├── providers/             # State management
│   │   ├── consultation_provider.dart
│   │   └── transcript_provider.dart
│   ├── models/                # Data models
│   │   ├── consultation.dart
│   │   ├── patient_info.dart
│   │   └── transcript_segment.dart
│   └── utils/                 # Constants and helpers
│       └── constants.dart
├── assets/                    # Static assets
├── pubspec.yaml              # Dependencies and configuration
└── README.md                 # This file
```

## Quick Start

### Prerequisites
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Android Studio / Xcode for mobile development
- DocScribe Backend server running

### Installation

```bash
# Clone the repository
cd flutter_app

# Install dependencies
flutter pub get

# Generate model files
flutter packages pub run build_runner build

# Run the app
flutter run
```

### Configuration

1. **Backend URL**: Update `ApiConstants.baseUrl` in `lib/utils/constants.dart`
2. **Permissions**: Audio recording permissions are handled automatically
3. **Platform Setup**: Follow Flutter's platform-specific setup guides

## Key Components

### State Management
Uses Provider pattern for reactive state management:

- **ConsultationProvider**: Manages consultation lifecycle
- **TranscriptProvider**: Handles real-time transcription

### Services

#### ApiService
HTTP client for backend communication:
```dart
// Start consultation
final consultationId = await apiService.startConsultation(patientInfo, doctorName);

// Send audio chunk
final response = await apiService.sendAudioChunk(audioData);

// Finish consultation
final transcript = await apiService.finishConsultation();
```

#### AudioService
High-quality audio recording optimized for NVIDIA Riva:
```dart
// Initialize audio service
await AudioService.instance.initialize();

// Start recording
await AudioService.instance.startRecording();

// Stop recording
final audioPath = await AudioService.instance.stopRecording();
```

### Models

#### PatientInfo
```dart
class PatientInfo {
  final String name;
  final int age;
  final String gender;
  final String? patientId;
  final String? reasonForVisit;
}
```

#### TranscriptSegment
```dart
class TranscriptSegment {
  final String text;
  final SpeakerType speaker;
  final double startTime;
  final double endTime;
  final double confidence;
}
```

## User Flow

### 1. Patient Information Entry
- Collect patient demographics
- Validate required fields
- Store securely for consultation

### 2. Audio Recording
- High-quality microphone capture
- Real-time audio level monitoring
- Live transcript preview
- Speaker diarization

### 3. Transcript Review
- Edit transcript segments
- Add doctor's notes
- Generate professional PDF
- Save to consultation history

### 4. History Management
- View past consultations
- Search and filter
- Share PDF reports
- Secure data management

## Audio Configuration

Optimized for NVIDIA Riva ASR:

```dart
static const int sampleRate = 16000; // 16kHz
static const int numChannels = 1;    // Mono
static const int bitDepth = 16;      // 16-bit
static const int chunkDurationMs = 100; // 100ms chunks
```

## UI/UX Design

### Design System
- **Colors**: Medical-grade color palette
- **Typography**: Clear, readable fonts
- **Icons**: Intuitive medical iconography
- **Layout**: Touch-friendly for clinical environments

### Accessibility
- Large touch targets for gloved hands
- High contrast for various lighting
- Screen reader support
- Keyboard navigation

### Responsive Design
- Tablet and phone layouts
- Portrait and landscape orientations
- Dynamic text sizing

## Security & Privacy

### Data Protection
- Local encryption for patient data
- Secure API communication (HTTPS)
- No unencrypted data persistence
- Configurable data retention

### HIPAA Compliance
- Audit logging
- Access controls
- Data minimization
- Secure transmission

### Permissions
- Microphone access for recording
- Storage access for PDF files
- Network access for API communication

## Development

### Code Generation
```bash
# Generate JSON serialization
flutter packages pub run build_runner build

# Watch for changes
flutter packages pub run build_runner watch
```

### Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

### Building

#### Android
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle
flutter build appbundle --release
```

#### iOS
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release
```

## Deployment

### Android
1. Configure signing keys
2. Update version in `pubspec.yaml`
3. Build release APK/Bundle
4. Upload to Google Play Console

### iOS
1. Configure provisioning profiles
2. Update version and build number
3. Build for release
4. Upload to App Store Connect

## Configuration

### Environment Variables
Update `lib/utils/constants.dart`:

```dart
class ApiConstants {
  static const String baseUrl = 'https://your-backend-url.com';
  // ... other constants
}
```

### App Configuration
Update `pubspec.yaml`:

```yaml
name: doc_scribe
description: Medical consultation transcription app
version: 1.0.0+1
```

## Troubleshooting

### Common Issues

1. **Audio Recording Failed**
   - Check microphone permissions
   - Verify audio service initialization
   - Test on physical device (not simulator)

2. **API Connection Error**
   - Verify backend server is running
   - Check network connectivity
   - Validate API endpoint URLs

3. **Build Errors**
   - Run `flutter clean`
   - Delete `pubspec.lock` and run `flutter pub get`
   - Check Flutter and Dart SDK versions

### Debug Mode
```bash
# Run with verbose logging
flutter run --verbose

# Enable debug mode
flutter run --debug
```

## Performance Optimization

### Audio Processing
- Efficient chunk processing
- Memory management for long recordings
- Background processing capabilities

### UI Performance
- Lazy loading for large lists
- Optimized animations
- Efficient state updates

### Storage
- Compressed audio formats
- Efficient PDF generation
- Smart caching strategies

## Platform-Specific Notes

### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: 34 (Android 14)
- Permissions: RECORD_AUDIO, WRITE_EXTERNAL_STORAGE

### iOS
- Minimum iOS: 12.0
- Privacy usage descriptions required
- Background audio capabilities

## Future Enhancements

- [ ] Offline transcription capabilities
- [ ] Cloud storage integration
- [ ] Multi-language UI support
- [ ] Advanced audio filters
- [ ] Telemedicine integration
- [ ] Voice biometrics
- [ ] AI-powered medical insights

## Support

For technical support:
1. Check Flutter doctor: `flutter doctor`
2. Review device logs
3. Test on multiple devices
4. Contact development team

## License

Proprietary software for medical transcription applications.
