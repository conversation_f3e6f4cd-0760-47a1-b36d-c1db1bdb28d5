// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Consultation _$ConsultationFromJson(Map<String, dynamic> json) => Consultation(
      id: json['id'] as String,
      patient: PatientInfo.fromJson(json['patient'] as Map<String, dynamic>),
      transcript: (json['transcript'] as List<dynamic>?)
              ?.map((e) => TranscriptSegment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      status: $enumDecodeNullable(_$ConsultationStatusEnumMap, json['status']) ??
          ConsultationStatus.draft,
      audioFilePath: json['audioFilePath'] as String?,
      pdfFilePath: json['pdfFilePath'] as String?,
      doctorName: json['doctorName'] as String?,
      doctorNotes: json['doctorNotes'] as String?,
      errorMessage: json['errorMessage'] as String?,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: json['duration'] as int),
    );

Map<String, dynamic> _$ConsultationToJson(Consultation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'patient': instance.patient.toJson(),
      'transcript': instance.transcript.map((e) => e.toJson()).toList(),
      'createdAt': instance.createdAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'status': _$ConsultationStatusEnumMap[instance.status]!,
      'audioFilePath': instance.audioFilePath,
      'pdfFilePath': instance.pdfFilePath,
      'doctorName': instance.doctorName,
      'doctorNotes': instance.doctorNotes,
      'errorMessage': instance.errorMessage,
      'duration': instance.duration?.inMicroseconds,
    };

const _$ConsultationStatusEnumMap = {
  ConsultationStatus.draft: 'draft',
  ConsultationStatus.recording: 'recording',
  ConsultationStatus.processing: 'processing',
  ConsultationStatus.completed: 'completed',
  ConsultationStatus.error: 'error',
};
