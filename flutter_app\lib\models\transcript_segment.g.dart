// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcript_segment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptSegment _$TranscriptSegmentFromJson(Map<String, dynamic> json) =>
    TranscriptSegment(
      id: json['id'] as String,
      text: json['text'] as String,
      speaker: $enumDecode(_$SpeakerTypeEnumMap, json['speaker']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      startTime: (json['startTime'] as num).toDouble(),
      endTime: (json['endTime'] as num).toDouble(),
      confidence: (json['confidence'] as num?)?.toDouble() ?? 1.0,
      isFinal: json['isFinal'] as bool? ?? true,
    );

Map<String, dynamic> _$TranscriptSegmentToJson(TranscriptSegment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'speaker': _$SpeakerTypeEnumMap[instance.speaker]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'confidence': instance.confidence,
      'isFinal': instance.isFinal,
    };

const _$SpeakerTypeEnumMap = {
  SpeakerType.doctor: 'doctor',
  SpeakerType.patient: 'patient',
  SpeakerType.other: 'other',
};
