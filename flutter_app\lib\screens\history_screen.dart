import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/consultation_provider.dart';
import '../utils/constants.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ConsultationProvider>().loadConsultationHistory();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.historyTitle),
        centerTitle: true,
      ),
      body: Consumer<ConsultationProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: AppSizes.paddingMedium),
                  Text(
                    'Error: ${provider.error}',
                    style: const TextStyle(color: AppColors.error),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizes.paddingMedium),
                  ElevatedButton(
                    onPressed: () => provider.loadConsultationHistory(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (provider.consultationHistory.isEmpty) {
            return _buildEmptyState();
          }

          return _buildHistoryList(provider.consultationHistory);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey.shade400,
          ),
          
          const SizedBox(height: AppSizes.paddingMedium),
          
          Text(
            'No consultations yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          Text(
            'Start your first consultation to see it here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textHint,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSizes.paddingLarge),
          
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Start New Consultation'),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(List consultations) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      itemCount: consultations.length,
      itemBuilder: (context, index) {
        final consultation = consultations[index];
        return _buildConsultationCard(consultation);
      },
    );
  }

  Widget _buildConsultationCard(consultation) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      child: InkWell(
        onTap: () => _viewConsultation(consultation),
        borderRadius: BorderRadius.circular(AppSizes.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          consultation.patient.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        
                        const SizedBox(height: 2),
                        
                        Text(
                          _formatDate(consultation.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  _buildStatusChip(consultation.status),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingMedium),
              
              // Details row
              Row(
                children: [
                  _buildDetailChip(
                    Icons.person,
                    '${consultation.patient.age}y, ${consultation.patient.gender}',
                  ),
                  
                  const SizedBox(width: AppSizes.paddingSmall),
                  
                  if (consultation.duration != null)
                    _buildDetailChip(
                      Icons.timer,
                      _formatDuration(consultation.duration),
                    ),
                  
                  const SizedBox(width: AppSizes.paddingSmall),
                  
                  if (consultation.hasTranscript)
                    _buildDetailChip(
                      Icons.text_fields,
                      '${consultation.wordCount} words',
                    ),
                ],
              ),
              
              if (consultation.patient.reasonForVisit != null) ...[
                const SizedBox(height: AppSizes.paddingSmall),
                
                Text(
                  'Reason: ${consultation.patient.reasonForVisit}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              // Action buttons
              const SizedBox(height: AppSizes.paddingMedium),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (consultation.hasPdf)
                    TextButton.icon(
                      onPressed: () => _viewPdf(consultation),
                      icon: const Icon(Icons.picture_as_pdf, size: AppSizes.iconSmall),
                      label: const Text('View PDF'),
                    ),
                  
                  TextButton.icon(
                    onPressed: () => _shareConsultation(consultation),
                    icon: const Icon(Icons.share, size: AppSizes.iconSmall),
                    label: const Text('Share'),
                  ),
                  
                  PopupMenuButton(
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: AppSizes.iconSmall, color: AppColors.error),
                            SizedBox(width: AppSizes.paddingSmall),
                            Text('Delete'),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'delete') {
                        _deleteConsultation(consultation);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ConsultationStatus status) {
    Color color;
    String label;
    
    switch (status) {
      case ConsultationStatus.completed:
        color = AppColors.success;
        label = 'Completed';
        break;
      case ConsultationStatus.processing:
        color = AppColors.warning;
        label = 'Processing';
        break;
      case ConsultationStatus.error:
        color = AppColors.error;
        label = 'Error';
        break;
      default:
        color = AppColors.textHint;
        label = 'Draft';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDetailChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppSizes.iconSmall,
            color: AppColors.textHint,
          ),
          
          const SizedBox(width: 4),
          
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _viewConsultation(consultation) {
    // TODO: Navigate to consultation detail view
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Consultation detail view coming soon')),
    );
  }

  void _viewPdf(consultation) {
    // TODO: Open PDF viewer
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PDF viewer coming soon')),
    );
  }

  void _shareConsultation(consultation) {
    // TODO: Implement sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing functionality coming soon')),
    );
  }

  void _deleteConsultation(consultation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Consultation'),
        content: Text(
          'Are you sure you want to delete the consultation for ${consultation.patient.name}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ConsultationProvider>().deleteConsultation(consultation.id);
            },
            child: const Text(
              AppStrings.delete,
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '0m';
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}
