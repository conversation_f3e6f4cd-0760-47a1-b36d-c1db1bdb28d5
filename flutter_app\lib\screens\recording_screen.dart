import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/consultation_provider.dart';
import '../providers/transcript_provider.dart';
import '../utils/constants.dart';
import '../widgets/audio_level_meter.dart';
import '../widgets/live_transcript_preview.dart';
import 'review_transcript_screen.dart';

class RecordingScreen extends StatefulWidget {
  const RecordingScreen({super.key});

  @override
  State<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends State<RecordingScreen> {
  bool _showTranscriptPreview = false;

  @override
  void initState() {
    super.initState();
    // Start live transcription when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TranscriptProvider>().startLiveTranscription();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.recordingTitle),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _onBackPressed,
        ),
      ),
      body: SafeArea(
        child: Consumer2<ConsultationProvider, TranscriptProvider>(
          builder: (context, consultationProvider, transcriptProvider, child) {
            final consultation = consultationProvider.currentConsultation;
            
            if (consultation == null) {
              return const Center(
                child: Text('No active consultation'),
              );
            }

            return Column(
              children: [
                // Patient info header
                _buildPatientHeader(consultation.patient.name),
                
                // Recording status and controls
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(AppSizes.paddingLarge),
                    child: Column(
                      children: [
                        // Audio level meter
                        const AudioLevelMeter(),
                        
                        const SizedBox(height: AppSizes.paddingXLarge),
                        
                        // Recording timer
                        _buildRecordingTimer(consultationProvider.recordingDuration),
                        
                        const SizedBox(height: AppSizes.paddingXLarge),
                        
                        // Recording controls
                        _buildRecordingControls(consultationProvider),
                        
                        const SizedBox(height: AppSizes.paddingLarge),
                        
                        // Transcript preview toggle
                        _buildTranscriptToggle(),
                        
                        const SizedBox(height: AppSizes.paddingMedium),
                        
                        // Live transcript preview
                        if (_showTranscriptPreview)
                          Expanded(
                            child: LiveTranscriptPreview(
                              liveTranscript: transcriptProvider.liveTranscript,
                              partialTranscript: transcriptProvider.partialTranscript,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                
                // Status bar
                _buildStatusBar(consultationProvider, transcriptProvider),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildPatientHeader(String patientName) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: AppColors.primary.withOpacity(0.3)),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.person,
            color: AppColors.primary,
            size: AppSizes.iconMedium,
          ),
          
          const SizedBox(width: AppSizes.paddingSmall),
          
          Text(
            'Patient: $patientName',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingTimer(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    return Column(
      children: [
        Text(
          'Recording Time',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        
        const SizedBox(height: AppSizes.paddingSmall),
        
        Text(
          '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
            fontFeatures: [const FontFeature.tabularFigures()],
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingControls(ConsultationProvider provider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Stop button
        _buildControlButton(
          icon: Icons.stop,
          label: AppStrings.stop,
          color: AppColors.error,
          onPressed: provider.isRecording ? () => _stopRecording() : null,
        ),
        
        // Main record button
        _buildMainRecordButton(provider),
        
        // Pause/Resume button
        _buildControlButton(
          icon: provider.isRecording ? Icons.pause : Icons.play_arrow,
          label: provider.isRecording ? AppStrings.pause : AppStrings.resume,
          color: AppColors.warning,
          onPressed: provider.isRecording ? () => _pauseRecording() : () => _resumeRecording(),
        ),
      ],
    );
  }

  Widget _buildMainRecordButton(ConsultationProvider provider) {
    final isRecording = provider.isRecording;
    final isLoading = provider.isLoading;
    
    return GestureDetector(
      onTap: isLoading ? null : (isRecording ? _stopRecording : _startRecording),
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isRecording ? AppColors.error : AppColors.primary,
          boxShadow: [
            BoxShadow(
              color: (isRecording ? AppColors.error : AppColors.primary).withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: isLoading
            ? const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              )
            : Icon(
                isRecording ? Icons.stop : Icons.mic,
                size: AppSizes.iconXLarge,
                color: Colors.white,
              ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onPressed,
  }) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
          ),
          child: Icon(
            icon,
            size: AppSizes.iconLarge,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: AppSizes.paddingSmall),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildTranscriptToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.text_snippet,
          size: AppSizes.iconSmall,
          color: AppColors.textSecondary,
        ),
        
        const SizedBox(width: AppSizes.paddingSmall),
        
        Text(
          'Live Transcript Preview',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        
        const SizedBox(width: AppSizes.paddingSmall),
        
        Switch(
          value: _showTranscriptPreview,
          onChanged: (value) {
            setState(() {
              _showTranscriptPreview = value;
            });
          },
          activeColor: AppColors.primary,
        ),
      ],
    );
  }

  Widget _buildStatusBar(
    ConsultationProvider consultationProvider,
    TranscriptProvider transcriptProvider,
  ) {
    String statusText = 'Ready to record';
    Color statusColor = AppColors.textSecondary;
    
    if (consultationProvider.isRecording) {
      statusText = 'Recording in progress...';
      statusColor = AppColors.success;
    } else if (consultationProvider.isLoading) {
      statusText = 'Processing...';
      statusColor = AppColors.warning;
    } else if (transcriptProvider.error != null) {
      statusText = 'Transcription error: ${transcriptProvider.error}';
      statusColor = AppColors.error;
    }
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: statusColor,
            ),
          ),
          
          const SizedBox(width: AppSizes.paddingSmall),
          
          Expanded(
            child: Text(
              statusText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startRecording() async {
    try {
      await context.read<ConsultationProvider>().startRecording();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start recording: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      await context.read<ConsultationProvider>().stopRecording();
      await context.read<TranscriptProvider>().stopLiveTranscription();
      
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const ReviewTranscriptScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to stop recording: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _pauseRecording() async {
    // Implementation for pause functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Pause functionality coming soon')),
    );
  }

  Future<void> _resumeRecording() async {
    // Implementation for resume functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Resume functionality coming soon')),
    );
  }

  void _onBackPressed() {
    if (context.read<ConsultationProvider>().isRecording) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Stop Recording?'),
          content: const Text('Are you sure you want to stop the current recording?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppStrings.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _stopRecording();
              },
              child: const Text(AppStrings.stop),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }
}
