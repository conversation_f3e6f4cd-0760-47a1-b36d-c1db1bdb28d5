import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/consultation_provider.dart';
import '../providers/transcript_provider.dart';
import '../utils/constants.dart';
import 'home_screen.dart';

class ReviewTranscriptScreen extends StatefulWidget {
  const ReviewTranscriptScreen({super.key});

  @override
  State<ReviewTranscriptScreen> createState() => _ReviewTranscriptScreenState();
}

class _ReviewTranscriptScreenState extends State<ReviewTranscriptScreen> {
  final _notesController = TextEditingController();
  bool _isGeneratingPdf = false;

  @override
  void initState() {
    super.initState();
    // Load final transcript
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final consultation = context.read<ConsultationProvider>().currentConsultation;
      if (consultation != null && consultation.transcript.isNotEmpty) {
        context.read<TranscriptProvider>().setFinalTranscript(consultation.transcript);
      }
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.reviewTitle),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => const HomeScreen()),
            (route) => false,
          ),
        ),
      ),
      body: Consumer2<ConsultationProvider, TranscriptProvider>(
        builder: (context, consultationProvider, transcriptProvider, child) {
          final consultation = consultationProvider.currentConsultation;
          
          if (consultation == null) {
            return const Center(child: Text('No consultation data available'));
          }

          return Column(
            children: [
              // Summary header
              _buildSummaryHeader(consultation, transcriptProvider),
              
              // Transcript content
              Expanded(
                child: _buildTranscriptContent(transcriptProvider),
              ),
              
              // Doctor notes section
              _buildNotesSection(),
              
              // Action buttons
              _buildActionButtons(consultationProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSummaryHeader(consultation, TranscriptProvider transcriptProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: AppColors.success.withOpacity(0.3)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: AppColors.success,
                size: AppSizes.iconMedium,
              ),
              
              const SizedBox(width: AppSizes.paddingSmall),
              
              Text(
                'Consultation Completed',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingMedium),
          
          Row(
            children: [
              _buildStatCard(
                'Patient',
                consultation.patient.name,
                Icons.person,
              ),
              
              const SizedBox(width: AppSizes.paddingMedium),
              
              _buildStatCard(
                'Duration',
                _formatDuration(consultation.duration ?? Duration.zero),
                Icons.timer,
              ),
              
              const SizedBox(width: AppSizes.paddingMedium),
              
              _buildStatCard(
                'Words',
                '${transcriptProvider.totalWords}',
                Icons.text_fields,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: AppSizes.iconMedium,
              color: AppColors.primary,
            ),
            
            const SizedBox(height: AppSizes.paddingSmall),
            
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranscriptContent(TranscriptProvider transcriptProvider) {
    if (transcriptProvider.finalTranscript.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.transcript,
              size: 64,
              color: AppColors.textHint,
            ),
            SizedBox(height: AppSizes.paddingMedium),
            Text(
              'No transcript available',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      itemCount: transcriptProvider.finalTranscript.length,
      itemBuilder: (context, index) {
        final segment = transcriptProvider.finalTranscript[index];
        return _buildTranscriptSegment(segment, index);
      },
    );
  }

  Widget _buildTranscriptSegment(segment, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingSmall,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: segment.speaker == SpeakerType.doctor 
                      ? AppColors.primary 
                      : AppColors.secondary,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                ),
                child: Text(
                  segment.speakerLabel,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(width: AppSizes.paddingSmall),
              
              Text(
                segment.formattedTimestamp,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textHint,
                ),
              ),
              
              const Spacer(),
              
              IconButton(
                icon: const Icon(Icons.edit, size: AppSizes.iconSmall),
                onPressed: () => _editSegment(segment, index),
                color: AppColors.textHint,
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          Text(
            segment.text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.doctorNotes,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          TextField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Add your notes, diagnosis, or treatment plan...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.borderRadius),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ConsultationProvider consultationProvider) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => _startNewConsultation(),
              child: const Text('New Consultation'),
            ),
          ),
          
          const SizedBox(width: AppSizes.paddingMedium),
          
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isGeneratingPdf ? null : _generatePdf,
              child: _isGeneratingPdf
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(AppStrings.generatePdf),
            ),
          ),
        ],
      ),
    );
  }

  void _editSegment(segment, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Transcript'),
        content: TextField(
          controller: TextEditingController(text: segment.text),
          maxLines: 3,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Edit transcript text...',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement edit functionality
              Navigator.of(context).pop();
            },
            child: const Text(AppStrings.save),
          ),
        ],
      ),
    );
  }

  Future<void> _generatePdf() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Add doctor notes to consultation
      if (_notesController.text.isNotEmpty) {
        context.read<ConsultationProvider>().addDoctorNotes(_notesController.text);
      }

      // Save consultation
      await context.read<ConsultationProvider>().saveConsultation();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF generated successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate PDF: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    }
  }

  void _startNewConsultation() {
    context.read<ConsultationProvider>().clearCurrentConsultation();
    context.read<TranscriptProvider>().clearTranscript();
    
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const HomeScreen()),
      (route) => false,
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}
