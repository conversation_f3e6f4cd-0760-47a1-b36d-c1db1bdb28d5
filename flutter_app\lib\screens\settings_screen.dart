import 'package:flutter/material.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _apiKeyController = TextEditingController();
  String _selectedLanguage = 'en-US';
  bool _enableDiarization = true;
  bool _autoSavePdf = true;
  
  final List<Map<String, String>> _languages = [
    {'code': 'en-US', 'name': 'English (US)'},
    {'code': 'en-GB', 'name': 'English (UK)'},
    {'code': 'es-ES', 'name': 'Spanish'},
    {'code': 'fr-FR', 'name': 'French'},
    {'code': 'de-DE', 'name': 'German'},
    {'code': 'it-IT', 'name': 'Italian'},
    {'code': 'pt-BR', 'name': 'Portuguese (Brazil)'},
    {'code': 'hi-IN', 'name': 'Hindi'},
    {'code': 'zh-CN', 'name': 'Chinese (Simplified)'},
    {'code': 'ja-JP', 'name': 'Japanese'},
  ];

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.settingsTitle),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        children: [
          // API Configuration Section
          _buildSectionHeader('API Configuration'),
          _buildApiKeyField(),
          _buildLanguageSelector(),
          
          const SizedBox(height: AppSizes.paddingLarge),
          
          // Recording Settings Section
          _buildSectionHeader('Recording Settings'),
          _buildSwitchTile(
            title: 'Speaker Diarization',
            subtitle: 'Automatically identify doctor and patient voices',
            value: _enableDiarization,
            onChanged: (value) => setState(() => _enableDiarization = value),
          ),
          
          const SizedBox(height: AppSizes.paddingLarge),
          
          // PDF Settings Section
          _buildSectionHeader('PDF Settings'),
          _buildSwitchTile(
            title: 'Auto-save PDF',
            subtitle: 'Automatically generate PDF after consultation',
            value: _autoSavePdf,
            onChanged: (value) => setState(() => _autoSavePdf = value),
          ),
          
          const SizedBox(height: AppSizes.paddingLarge),
          
          // About Section
          _buildSectionHeader('About'),
          _buildInfoTile(
            title: 'Version',
            subtitle: '1.0.0',
            icon: Icons.info_outline,
          ),
          
          _buildInfoTile(
            title: 'NVIDIA Riva Model',
            subtitle: 'Canary-1B ASR',
            icon: Icons.memory,
          ),
          
          const SizedBox(height: AppSizes.paddingLarge),
          
          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildApiKeyField() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.key,
                  size: AppSizes.iconMedium,
                  color: AppColors.primary,
                ),
                
                const SizedBox(width: AppSizes.paddingSmall),
                
                Text(
                  'NVIDIA Riva API Key',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingSmall),
            
            TextField(
              controller: _apiKeyController,
              obscureText: true,
              decoration: InputDecoration(
                hintText: 'Enter your NVIDIA Riva API key',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.visibility),
                  onPressed: () {
                    // Toggle visibility
                  },
                ),
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingSmall),
            
            Text(
              'Get your API key from NVIDIA NGC catalog',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.language,
                  size: AppSizes.iconMedium,
                  color: AppColors.primary,
                ),
                
                const SizedBox(width: AppSizes.paddingSmall),
                
                Text(
                  'Default Language',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingSmall),
            
            DropdownButtonFormField<String>(
              value: _selectedLanguage,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                ),
              ),
              items: _languages.map((language) {
                return DropdownMenuItem(
                  value: language['code'],
                  child: Text(language['name']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      child: SwitchListTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Card(
      child: ListTile(
        leading: Icon(
          icon,
          color: AppColors.primary,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _saveSettings,
            child: const Text('Save Settings'),
          ),
        ),
        
        const SizedBox(height: AppSizes.paddingMedium),
        
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _resetSettings,
            child: const Text('Reset to Defaults'),
          ),
        ),
        
        const SizedBox(height: AppSizes.paddingMedium),
        
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _testConnection,
            child: const Text('Test API Connection'),
          ),
        ),
      ],
    );
  }

  void _saveSettings() {
    // TODO: Implement settings save
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _apiKeyController.clear();
                _selectedLanguage = 'en-US';
                _enableDiarization = true;
                _autoSavePdf = true;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _testConnection() {
    // TODO: Implement API connection test
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Testing API connection...')),
    );
  }
}
