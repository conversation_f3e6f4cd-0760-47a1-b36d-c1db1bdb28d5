import 'dart:convert';
import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/consultation.dart';
import '../utils/constants.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  static StorageService get instance => _instance;
  StorageService._internal();

  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  static const String _consultationsKey = 'consultations';
  static const String _settingsKey = 'app_settings';
  static const String _doctorNameKey = 'doctor_name';
  static const String _apiKeyKey = 'api_key';

  /// Initialize storage service
  Future<void> initialize() async {
    try {
      // Ensure directories exist
      await _ensureDirectoriesExist();
    } catch (e) {
      throw StorageException('Failed to initialize storage: $e');
    }
  }

  /// Save consultation to secure storage
  Future<void> saveConsultation(Consultation consultation) async {
    try {
      final consultations = await getConsultations();
      
      // Update existing or add new
      final index = consultations.indexWhere((c) => c.id == consultation.id);
      if (index >= 0) {
        consultations[index] = consultation;
      } else {
        consultations.insert(0, consultation); // Add to beginning
      }

      // Keep only last 100 consultations
      if (consultations.length > 100) {
        consultations.removeRange(100, consultations.length);
      }

      await _saveConsultations(consultations);
    } catch (e) {
      throw StorageException('Failed to save consultation: $e');
    }
  }

  /// Get all consultations
  Future<List<Consultation>> getConsultations() async {
    try {
      final consultationsJson = await _secureStorage.read(key: _consultationsKey);
      if (consultationsJson == null) return [];

      final List<dynamic> consultationsList = jsonDecode(consultationsJson);
      return consultationsList
          .map((json) => Consultation.fromJson(json))
          .toList();
    } catch (e) {
      throw StorageException('Failed to load consultations: $e');
    }
  }

  /// Get consultation by ID
  Future<Consultation?> getConsultation(String id) async {
    try {
      final consultations = await getConsultations();
      return consultations.firstWhere(
        (c) => c.id == id,
        orElse: () => throw StorageException('Consultation not found'),
      );
    } catch (e) {
      return null;
    }
  }

  /// Delete consultation
  Future<void> deleteConsultation(String id) async {
    try {
      final consultations = await getConsultations();
      consultations.removeWhere((c) => c.id == id);
      await _saveConsultations(consultations);

      // Also delete associated files
      await _deleteConsultationFiles(id);
    } catch (e) {
      throw StorageException('Failed to delete consultation: $e');
    }
  }

  /// Save app settings
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    try {
      await _secureStorage.write(
        key: _settingsKey,
        value: jsonEncode(settings),
      );
    } catch (e) {
      throw StorageException('Failed to save settings: $e');
    }
  }

  /// Get app settings
  Future<Map<String, dynamic>> getSettings() async {
    try {
      final settingsJson = await _secureStorage.read(key: _settingsKey);
      if (settingsJson == null) return _getDefaultSettings();

      return Map<String, dynamic>.from(jsonDecode(settingsJson));
    } catch (e) {
      return _getDefaultSettings();
    }
  }

  /// Save doctor name
  Future<void> saveDoctorName(String doctorName) async {
    try {
      await _secureStorage.write(key: _doctorNameKey, value: doctorName);
    } catch (e) {
      throw StorageException('Failed to save doctor name: $e');
    }
  }

  /// Get doctor name
  Future<String?> getDoctorName() async {
    try {
      return await _secureStorage.read(key: _doctorNameKey);
    } catch (e) {
      return null;
    }
  }

  /// Save API key
  Future<void> saveApiKey(String apiKey) async {
    try {
      await _secureStorage.write(key: _apiKeyKey, value: apiKey);
    } catch (e) {
      throw StorageException('Failed to save API key: $e');
    }
  }

  /// Get API key
  Future<String?> getApiKey() async {
    try {
      return await _secureStorage.read(key: _apiKeyKey);
    } catch (e) {
      return null;
    }
  }

  /// Export consultations to JSON file
  Future<String> exportConsultations() async {
    try {
      final consultations = await getConsultations();
      final exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'consultations': consultations.map((c) => c.toJson()).toList(),
      };

      final directory = await getApplicationDocumentsDirectory();
      final exportDir = Directory(path.join(directory.path, 'DocScribe', 'Exports'));
      await exportDir.create(recursive: true);

      final fileName = 'consultations_export_${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = path.join(exportDir.path, fileName);

      final file = File(filePath);
      await file.writeAsString(jsonEncode(exportData));

      return filePath;
    } catch (e) {
      throw StorageException('Failed to export consultations: $e');
    }
  }

  /// Import consultations from JSON file
  Future<int> importConsultations(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw StorageException('Import file not found');
      }

      final content = await file.readAsString();
      final importData = jsonDecode(content);

      if (importData['consultations'] == null) {
        throw StorageException('Invalid import file format');
      }

      final List<dynamic> consultationsList = importData['consultations'];
      final importedConsultations = consultationsList
          .map((json) => Consultation.fromJson(json))
          .toList();

      // Merge with existing consultations
      final existingConsultations = await getConsultations();
      final allConsultations = [...existingConsultations];

      int importedCount = 0;
      for (final consultation in importedConsultations) {
        if (!allConsultations.any((c) => c.id == consultation.id)) {
          allConsultations.add(consultation);
          importedCount++;
        }
      }

      // Sort by creation date (newest first)
      allConsultations.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      await _saveConsultations(allConsultations);
      return importedCount;
    } catch (e) {
      throw StorageException('Failed to import consultations: $e');
    }
  }

  /// Clear all data
  Future<void> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      await _deleteAllFiles();
    } catch (e) {
      throw StorageException('Failed to clear data: $e');
    }
  }

  /// Get storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final consultations = await getConsultations();
      final directory = await getApplicationDocumentsDirectory();
      final docScribeDir = Directory(path.join(directory.path, 'DocScribe'));

      int totalFiles = 0;
      int totalSize = 0;

      if (await docScribeDir.exists()) {
        await for (final entity in docScribeDir.list(recursive: true)) {
          if (entity is File) {
            totalFiles++;
            totalSize += await entity.length();
          }
        }
      }

      return {
        'total_consultations': consultations.length,
        'completed_consultations': consultations.where((c) => c.isCompleted).length,
        'total_files': totalFiles,
        'total_size_bytes': totalSize,
        'total_size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
      };
    } catch (e) {
      return {
        'total_consultations': 0,
        'completed_consultations': 0,
        'total_files': 0,
        'total_size_bytes': 0,
        'total_size_mb': '0.00',
      };
    }
  }

  /// Private helper methods
  Future<void> _saveConsultations(List<Consultation> consultations) async {
    final consultationsJson = jsonEncode(
      consultations.map((c) => c.toJson()).toList(),
    );
    await _secureStorage.write(key: _consultationsKey, value: consultationsJson);
  }

  Future<void> _ensureDirectoriesExist() async {
    final directory = await getApplicationDocumentsDirectory();
    final directories = [
      'DocScribe',
      'DocScribe/Audio',
      'DocScribe/PDFs',
      'DocScribe/Exports',
    ];

    for (final dir in directories) {
      final dirPath = Directory(path.join(directory.path, dir));
      await dirPath.create(recursive: true);
    }
  }

  Future<void> _deleteConsultationFiles(String consultationId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final docScribeDir = Directory(path.join(directory.path, 'DocScribe'));

      if (await docScribeDir.exists()) {
        await for (final entity in docScribeDir.list(recursive: true)) {
          if (entity is File && entity.path.contains(consultationId)) {
            await entity.delete();
          }
        }
      }
    } catch (e) {
      // Log error but don't throw - file deletion is not critical
      print('Warning: Failed to delete consultation files: $e');
    }
  }

  Future<void> _deleteAllFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final docScribeDir = Directory(path.join(directory.path, 'DocScribe'));

      if (await docScribeDir.exists()) {
        await docScribeDir.delete(recursive: true);
      }
    } catch (e) {
      // Log error but don't throw
      print('Warning: Failed to delete all files: $e');
    }
  }

  Map<String, dynamic> _getDefaultSettings() {
    return {
      'language': 'en-US',
      'auto_save_pdf': true,
      'enable_diarization': true,
      'audio_quality': 'high',
      'theme': 'light',
    };
  }
}

class StorageException implements Exception {
  final String message;
  StorageException(this.message);

  @override
  String toString() => 'StorageException: $message';
}
