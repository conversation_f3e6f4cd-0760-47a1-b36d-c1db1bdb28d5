import 'package:flutter/material.dart';
import '../models/transcript_segment.dart';
import '../utils/constants.dart';

class LiveTranscriptPreview extends StatefulWidget {
  final List<TranscriptSegment> liveTranscript;
  final String partialTranscript;

  const LiveTranscriptPreview({
    super.key,
    required this.liveTranscript,
    required this.partialTranscript,
  });

  @override
  State<LiveTranscriptPreview> createState() => _LiveTranscriptPreviewState();
}

class _LiveTranscriptPreviewState extends State<LiveTranscriptPreview> {
  final ScrollController _scrollController = ScrollController();

  @override
  void didUpdateWidget(LiveTranscriptPreview oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Auto-scroll to bottom when new content is added
    if (widget.liveTranscript.length != oldWidget.liveTranscript.length ||
        widget.partialTranscript != oldWidget.partialTranscript) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.borderRadiusLarge),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppSizes.borderRadiusLarge),
                topRight: Radius.circular(AppSizes.borderRadiusLarge),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.text_snippet,
                  size: AppSizes.iconSmall,
                  color: AppColors.primary,
                ),
                
                const SizedBox(width: AppSizes.paddingSmall),
                
                Text(
                  'Live Transcript',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
                
                const Spacer(),
                
                if (widget.liveTranscript.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.paddingSmall,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                    ),
                    child: Text(
                      '${widget.liveTranscript.length}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // Transcript content
          Expanded(
            child: widget.liveTranscript.isEmpty && widget.partialTranscript.isEmpty
                ? _buildEmptyState()
                : _buildTranscriptContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic_off,
            size: AppSizes.iconXLarge,
            color: Colors.grey.shade400,
          ),
          
          const SizedBox(height: AppSizes.paddingMedium),
          
          Text(
            'Waiting for speech...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          Text(
            'Start speaking to see live transcription',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textHint,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptContent() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      itemCount: widget.liveTranscript.length + (widget.partialTranscript.isNotEmpty ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < widget.liveTranscript.length) {
          // Final transcript segment
          return _buildTranscriptSegment(
            widget.liveTranscript[index],
            isFinal: true,
          );
        } else {
          // Partial transcript
          return _buildPartialTranscript();
        }
      },
    );
  }

  Widget _buildTranscriptSegment(TranscriptSegment segment, {required bool isFinal}) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Speaker and timestamp
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingSmall,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: _getSpeakerColor(segment.speaker),
                  borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                ),
                child: Text(
                  segment.speakerLabel,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(width: AppSizes.paddingSmall),
              
              Text(
                segment.formattedTimestamp,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textHint,
                ),
              ),
              
              const Spacer(),
              
              if (isFinal)
                const Icon(
                  Icons.check_circle,
                  size: AppSizes.iconSmall,
                  color: AppColors.success,
                ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          // Transcript text
          Text(
            segment.text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isFinal ? AppColors.textPrimary : AppColors.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPartialTranscript() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Partial indicator
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingSmall,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: AppColors.warning,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                ),
                child: Text(
                  'Listening...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(width: AppSizes.paddingSmall),
              
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.warning,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          // Partial text with typing indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.partialTranscript,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                    height: 1.4,
                  ),
                ),
              ),
              
              _buildTypingIndicator(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(left: AppSizes.paddingSmall),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300 + (index * 100)),
            margin: const EdgeInsets.symmetric(horizontal: 1),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textHint,
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }

  Color _getSpeakerColor(SpeakerType speaker) {
    switch (speaker) {
      case SpeakerType.doctor:
        return AppColors.primary;
      case SpeakerType.patient:
        return AppColors.secondary;
      case SpeakerType.other:
        return AppColors.textHint;
    }
  }
}
