name: doc_scribe
description: Medical consultation transcription app with NVIDIA Riva ASR integration
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

  # State Management
  provider: ^6.1.1

  # UI Components
  intl: ^0.19.0
  uuid: ^4.3.3

  # Audio Recording
  permission_handler: ^11.3.0
  path_provider: ^2.1.2
  flutter_sound: ^9.2.13
  audio_waveforms: ^1.0.5

  # HTTP Client for Backend API
  http: ^1.1.0
  dio: ^5.4.0

  # PDF Generation & Viewing
  pdf: ^3.10.7
  printing: ^5.12.0

  # File Sharing
  share_plus: ^7.2.2

  # Local Storage
  path: ^1.8.3
  sqflite: ^2.3.0
  flutter_secure_storage: ^9.0.0

  # JSON Serialization
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/

  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
