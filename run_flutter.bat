@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                DocScribe Flutter App                         ║
echo ║                                                              ║
echo ║              Starting mobile frontend...                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📱 Starting DocScribe Flutter app...
echo.

REM Check if flutter_app directory exists
if not exist "flutter_app" (
    echo ❌ Flutter app directory not found!
    echo    Run setup_flutter.bat first
    pause
    exit /b 1
)

cd flutter_app

REM Check for devices
echo 📱 Available devices:
flutter devices
echo.

REM Ask user which device to use
echo Choose a device:
echo 1. Windows (Desktop)
echo 2. Chrome (Web)
echo 3. Edge (Web)
echo 4. Let Flutter choose
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo 🖥️ Running on Windows Desktop...
    flutter run -d windows
) else if "%choice%"=="2" (
    echo 🌐 Running on Chrome...
    flutter run -d chrome
) else if "%choice%"=="3" (
    echo 🌐 Running on Edge...
    flutter run -d edge
) else (
    echo 🚀 Running on default device...
    flutter run
)

echo.
echo 👋 Flutter app stopped
cd ..
pause
