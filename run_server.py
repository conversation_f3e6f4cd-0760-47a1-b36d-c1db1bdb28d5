#!/usr/bin/env python3
"""
Simple server runner for DocScribe backend
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 Starting DocScribe Backend Server...")
    
    # Check if we're in the right directory
    if not Path("backend").exists():
        print("❌ Backend directory not found!")
        print("   Make sure you're in the DocScribe project directory")
        return False
    
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    # Check virtual environment
    if not venv_dir.exists():
        print("❌ Virtual environment not found!")
        print("   Run: python setup_backend_auto.py")
        return False
    
    # Determine python path (absolute path)
    if os.name == 'nt':  # Windows
        python_path = Path.cwd() / backend_dir / "venv" / "Scripts" / "python.exe"
    else:
        python_path = Path.cwd() / backend_dir / "venv" / "bin" / "python"
    
    if not python_path.exists():
        print(f"❌ Python not found at: {python_path}")
        return False
    
    print("✅ Environment ready")
    print("🌐 Starting server at: http://localhost:8000")
    print("📚 API docs will be at: http://localhost:8000/docs")
    print("💡 Press Ctrl+C to stop")
    print()
    
    # Change to backend directory and run server
    os.chdir(backend_dir)
    
    try:
        # Run uvicorn
        subprocess.run([
            str(python_path),
            "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
