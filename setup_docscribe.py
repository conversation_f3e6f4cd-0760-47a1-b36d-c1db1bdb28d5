#!/usr/bin/env python3
"""
DocScribe Setup Script

This script helps you set up the DocScribe application for the first time:
- Environment configuration
- Dependency installation
- NVIDIA Riva API key setup
- Database initialization
- Development environment setup
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import getpass

def print_setup_banner():
    """Print setup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                   DocScribe Setup                            ║
    ║                                                              ║
    ║              Medical Transcription System                    ║
    ║                   Initial Configuration                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """Check system prerequisites"""
    print("🔍 Checking system prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required (current: {sys.version.split()[0]})")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check Git
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        print("✅ Git available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Git not found (optional)")
    
    # Check Flutter
    try:
        result = subprocess.run(["flutter", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✅ {version}")
        else:
            print("⚠️  Flutter not properly configured")
    except FileNotFoundError:
        print("⚠️  Flutter not found")
        print("   Install from: https://flutter.dev/docs/get-started/install")
    
    return True

def setup_backend_environment():
    """Set up backend Python environment"""
    print("\n🐍 Setting up Python backend environment...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Create virtual environment
    venv_dir = backend_dir / "venv"
    if venv_dir.exists():
        print("⚠️  Virtual environment already exists")
        response = input("   Recreate it? (y/N): ").lower()
        if response == 'y':
            shutil.rmtree(venv_dir)
        else:
            print("   Using existing virtual environment")
    
    if not venv_dir.exists():
        print("Creating virtual environment...")
        try:
            subprocess.run([
                sys.executable, "-m", "venv", str(venv_dir)
            ], check=True, cwd=backend_dir)
            print("✅ Virtual environment created")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False
    
    # Install dependencies
    print("Installing Python dependencies...")
    try:
        if os.name == 'nt':  # Windows
            pip_path = venv_dir / "Scripts" / "pip.exe"
            python_path = venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            pip_path = venv_dir / "bin" / "pip"
            python_path = venv_dir / "bin" / "python"

        # Upgrade pip first
        print("Upgrading pip...")
        subprocess.run([
            str(python_path), "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, cwd=backend_dir)

        # Install requirements
        print("Installing requirements...")
        subprocess.run([
            str(python_path), "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, cwd=backend_dir)
        
        print("✅ Python dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment_file():
    """Set up environment configuration file"""
    print("\n⚙️  Setting up environment configuration...")
    
    backend_dir = Path("backend")
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    # Copy example file if .env doesn't exist
    if not env_file.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
    else:
        print("⚠️  .env file already exists")
        response = input("   Overwrite with template? (y/N): ").lower()
        if response == 'y':
            shutil.copy(env_example, env_file)
            print("✅ Overwrote .env file with template")
    
    # Configure NVIDIA Riva API key
    print("\n🔑 NVIDIA Riva API Configuration")
    print("   Get your API key from: https://catalog.ngc.nvidia.com/")
    print("   1. Sign up/login to NVIDIA NGC")
    print("   2. Navigate to Riva ASR models")
    print("   3. Generate an API key")
    
    api_key = getpass.getpass("   Enter your NVIDIA Riva API key (hidden): ").strip()
    
    if api_key:
        # Update .env file with API key
        try:
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Replace the API key line
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('RIVA_API_KEY='):
                    lines[i] = f'RIVA_API_KEY={api_key}'
                    break
            
            with open(env_file, 'w') as f:
                f.write('\n'.join(lines))
            
            print("✅ NVIDIA Riva API key configured")
            
        except Exception as e:
            print(f"❌ Failed to update .env file: {e}")
            return False
    else:
        print("⚠️  No API key provided - you'll need to edit .env manually")
    
    # Configure other settings
    print("\n📋 Additional Configuration")
    
    clinic_name = input("   Clinic/Hospital name (optional): ").strip()
    if clinic_name:
        try:
            with open(env_file, 'r') as f:
                content = f.read()
            
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('CLINIC_NAME='):
                    lines[i] = f'CLINIC_NAME={clinic_name}'
                    break
            
            with open(env_file, 'w') as f:
                f.write('\n'.join(lines))
            
            print(f"✅ Clinic name set to: {clinic_name}")
            
        except Exception as e:
            print(f"⚠️  Failed to update clinic name: {e}")
    
    return True

def setup_flutter_environment():
    """Set up Flutter environment"""
    print("\n📱 Setting up Flutter environment...")
    
    flutter_dir = Path("flutter_app")
    if not flutter_dir.exists():
        print("❌ Flutter app directory not found")
        return False
    
    # Check if Flutter is available
    try:
        subprocess.run(["flutter", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Flutter not available, skipping Flutter setup")
        return True
    
    try:
        # Get Flutter dependencies
        print("Getting Flutter dependencies...")
        subprocess.run([
            "flutter", "pub", "get"
        ], check=True, cwd=flutter_dir)
        
        # Generate model files
        print("Generating model files...")
        result = subprocess.run([
            "flutter", "packages", "pub", "run", "build_runner", "build"
        ], cwd=flutter_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("⚠️  Code generation had issues (this is normal for first setup)")
        
        print("✅ Flutter environment configured")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to setup Flutter environment: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating application directories...")
    
    directories = [
        "backend/storage",
        "backend/storage/audio",
        "backend/storage/pdfs",
        "backend/logs",
        "backend/templates",
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ Application directories created")

def run_initial_tests():
    """Run initial tests to verify setup"""
    print("\n🧪 Running initial tests...")
    
    # Test backend import
    try:
        backend_dir = Path("backend")
        venv_dir = backend_dir / "venv"
        
        if os.name == 'nt':  # Windows
            python_path = venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            python_path = venv_dir / "bin" / "python"
        
        # Test basic imports
        test_script = """
import sys
sys.path.append('.')
try:
    from app.core.config import settings
    print("✅ Backend configuration loaded")
    print(f"   Environment: {settings.ENVIRONMENT}")
    print(f"   Riva Server: {settings.RIVA_SERVER}")
except Exception as e:
    print(f"❌ Backend test failed: {e}")
    sys.exit(1)
"""
        
        result = subprocess.run([
            str(python_path), "-c", test_script
        ], cwd=backend_dir, capture_output=True, text=True)
        
        print(result.stdout)
        if result.returncode != 0:
            print(f"⚠️  Backend test warnings: {result.stderr}")
        
    except Exception as e:
        print(f"⚠️  Backend test failed: {e}")
    
    # Test Flutter
    flutter_dir = Path("flutter_app")
    if flutter_dir.exists():
        try:
            result = subprocess.run([
                "flutter", "analyze"
            ], cwd=flutter_dir, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Flutter analysis passed")
            else:
                print("⚠️  Flutter analysis had warnings")
                
        except Exception as e:
            print(f"⚠️  Flutter test failed: {e}")

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("   1. Review backend/.env file and update any additional settings")
    print("   2. Start the application:")
    print("      python start_docscribe.py")
    print("   3. Access the API documentation:")
    print("      http://localhost:8000/docs")
    print("   4. Test with a sample consultation")
    print("\n📚 Documentation:")
    print("   • Backend: backend/README.md")
    print("   • Flutter: flutter_app/README.md")
    print("   • NVIDIA Riva: https://docs.nvidia.com/deeplearning/riva/")
    print("\n🆘 Support:")
    print("   • Check logs in backend/logs/")
    print("   • Review environment configuration")
    print("   • Verify NVIDIA Riva API key")

def main():
    """Main setup function"""
    print_setup_banner()
    
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed")
        sys.exit(1)
    
    if not setup_backend_environment():
        print("\n❌ Backend setup failed")
        sys.exit(1)
    
    if not setup_environment_file():
        print("\n❌ Environment configuration failed")
        sys.exit(1)
    
    setup_flutter_environment()  # Non-critical
    
    create_directories()
    
    run_initial_tests()
    
    print_next_steps()

if __name__ == "__main__":
    main()
