@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                DocScribe Flutter Setup                       ║
echo ║                                                              ║
echo ║              Setting up mobile frontend...                  ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📱 Setting up Flutter app...
echo.

REM Check if flutter_app directory exists
if not exist "flutter_app" (
    echo ❌ Flutter app directory not found!
    echo    Make sure you're in the DocScribe project directory
    pause
    exit /b 1
)

cd flutter_app

REM Check Flutter installation
echo 🔍 Checking Flutter installation...
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Flutter not found!
    echo    Install Flutter from: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo ✅ Flutter found
echo.

REM Run Flutter doctor
echo 🔍 Running Flutter doctor...
flutter doctor
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean
if errorlevel 1 (
    echo ⚠️ Clean failed, continuing...
) else (
    echo ✅ Clean completed
)
echo.

REM Get dependencies
echo 📦 Getting Flutter dependencies...
flutter pub get
if errorlevel 1 (
    echo ❌ Failed to get dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed
echo.

REM Generate code (optional)
echo 🔧 Generating model files...
flutter packages pub run build_runner build
if errorlevel 1 (
    echo ⚠️ Code generation had issues (this is normal for first setup)
) else (
    echo ✅ Code generation completed
)
echo.

REM Check for devices
echo 📱 Checking for devices...
flutter devices
echo.

REM Test analysis
echo 🧪 Running Flutter analysis...
flutter analyze
if errorlevel 1 (
    echo ⚠️ Analysis had issues
) else (
    echo ✅ Analysis completed
)
echo.

cd ..

echo 🎉 Flutter setup completed!
echo.
echo 📋 Next Steps:
echo    1. Connect a device or start an emulator
echo    2. Run: run_flutter.bat
echo    3. Or manually: cd flutter_app ^&^& flutter run
echo.
echo 📱 Device Setup:
echo    • Android: Enable USB debugging
echo    • iOS: Trust developer certificate  
echo    • Emulator: Start Android Studio/Xcode simulator
echo.

pause
