#!/usr/bin/env python3
"""
Flutter Frontend Setup for DocScribe

This script sets up the Flutter frontend automatically.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    print("""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║                DocScribe Flutter Setup                       ║
║                                                              ║
║              Setting up mobile frontend...                  ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
""")

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=True if isinstance(cmd, str) else False
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            raise
        return e

def check_flutter():
    """Check if Flutter is available"""
    try:
        result = run_command(["flutter", "--version"])
        print(f"✅ Flutter found")
        return True
    except:
        print("❌ Flutter not found!")
        print("   Install Flutter from: https://flutter.dev/docs/get-started/install")
        return False

def setup_flutter_app():
    """Set up the Flutter application"""
    print_banner()
    
    # Check if Flutter is available
    if not check_flutter():
        return False
    
    # Check if flutter_app directory exists
    flutter_dir = Path("flutter_app")
    if not flutter_dir.exists():
        print("❌ Flutter app directory not found!")
        return False
    
    print("📱 Setting up Flutter app...")
    
    # Run flutter doctor
    print("🔍 Running Flutter doctor...")
    try:
        run_command(["flutter", "doctor"], cwd=flutter_dir, check=False)
    except:
        print("⚠️ Flutter doctor had issues, continuing...")
    
    # Clean previous builds
    print("🧹 Cleaning previous builds...")
    try:
        run_command(["flutter", "clean"], cwd=flutter_dir)
        print("✅ Clean completed")
    except:
        print("⚠️ Clean failed, continuing...")
    
    # Get dependencies
    print("📦 Getting Flutter dependencies...")
    try:
        run_command(["flutter", "pub", "get"], cwd=flutter_dir)
        print("✅ Dependencies installed")
    except Exception as e:
        print(f"❌ Failed to get dependencies: {e}")
        return False
    
    # Generate code (if needed)
    print("🔧 Generating model files...")
    try:
        run_command([
            "flutter", "packages", "pub", "run", "build_runner", "build"
        ], cwd=flutter_dir, check=False)
        print("✅ Code generation completed")
    except:
        print("⚠️ Code generation had issues (this is normal for first setup)")
    
    # Check for connected devices
    print("📱 Checking for devices...")
    try:
        result = run_command(["flutter", "devices"], cwd=flutter_dir, check=False)
        if "No devices found" in result.stdout:
            print("⚠️ No devices found")
            print("   Connect a device or start an emulator to run the app")
        else:
            print("✅ Devices found")
    except:
        print("⚠️ Could not check devices")
    
    # Test compilation
    print("🧪 Testing app compilation...")
    try:
        run_command([
            "flutter", "analyze"
        ], cwd=flutter_dir, check=False)
        print("✅ Analysis completed")
    except:
        print("⚠️ Analysis had issues")
    
    print("\n🎉 Flutter setup completed!")
    print("\n📋 Next Steps:")
    print("   1. Connect a device or start an emulator")
    print("   2. Run: python run_flutter.py")
    print("   3. Or manually: cd flutter_app && flutter run")
    
    print("\n📱 Device Setup:")
    print("   • Android: Enable USB debugging")
    print("   • iOS: Trust developer certificate")
    print("   • Emulator: Start Android Studio/Xcode simulator")
    
    return True

if __name__ == "__main__":
    try:
        success = setup_flutter_app()
        if success:
            print("\n✅ Flutter setup completed successfully!")
        else:
            print("\n❌ Flutter setup failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
    
    input("\nPress Enter to exit...")
