@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                   DocScribe Setup (Windows)                  ║
echo ║                                                              ║
echo ║              Medical Transcription System                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Setting up DocScribe for Windows...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Navigate to backend directory
if not exist "backend" (
    echo ❌ Backend directory not found!
    echo    Make sure you're in the DocScribe project directory
    pause
    exit /b 1
)

cd backend

REM Create virtual environment
echo 🐍 Creating virtual environment...
if exist "venv" (
    echo    Virtual environment already exists, removing old one...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment created
echo.

REM Activate virtual environment and install dependencies
echo 📦 Installing dependencies...
call venv\Scripts\activate.bat

REM Upgrade pip
python -m pip install --upgrade pip

REM Install requirements
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed
echo.

REM Setup environment file
if not exist ".env" (
    echo ⚙️ Setting up environment file...
    copy .env.example .env
    echo ✅ Created .env file from template
    echo.
    echo 🔑 IMPORTANT: Edit backend\.env and add your NVIDIA Riva API key
    echo    Get your API key from: https://catalog.ngc.nvidia.com/
    echo    Find the line: RIVA_API_KEY=your_nvidia_riva_api_key_here
    echo    Replace with: RIVA_API_KEY=your_actual_api_key
    echo.
) else (
    echo ✅ Environment file already exists
)

REM Create directories
echo 📁 Creating directories...
if not exist "storage" mkdir storage
if not exist "storage\audio" mkdir storage\audio
if not exist "storage\pdfs" mkdir storage\pdfs
if not exist "logs" mkdir logs
if not exist "templates" mkdir templates

echo ✅ Directories created
echo.

cd ..

echo 🎉 Setup completed successfully!
echo.
echo 📋 Next Steps:
echo    1. Edit backend\.env and add your NVIDIA Riva API key
echo    2. Run: start_windows.bat
echo    3. Access API at: http://localhost:8000
echo.
echo 📚 Documentation:
echo    • API Docs: http://localhost:8000/docs
echo    • Backend README: backend\README.md
echo.

pause
