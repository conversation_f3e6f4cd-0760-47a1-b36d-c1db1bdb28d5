@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                DocScribe Complete Application                ║
echo ║                                                              ║
echo ║              Starting Backend + Frontend...                 ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting DocScribe Complete Application...
echo.

REM Check if backend is set up
if not exist "backend\venv" (
    echo ❌ Backend not set up!
    echo    Run: python setup_backend_auto.py
    pause
    exit /b 1
)

REM Check if flutter app is set up
if not exist "flutter_app\pubspec.yaml" (
    echo ❌ Flutter app not set up!
    echo    Run: setup_flutter.bat
    pause
    exit /b 1
)

echo 📋 Starting services:
echo    1. Backend API Server (Python FastAPI)
echo    2. Flutter Web App (Chrome)
echo.

REM Start backend in background
echo 🐍 Starting backend server...
start "DocScribe Backend" /min python run_server.py

REM Wait for backend to start
echo    Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

REM Start Flutter app
echo 📱 Starting Flutter web app...
cd flutter_app
start "DocScribe Flutter" flutter run -d chrome

echo.
echo 🎉 DocScribe is starting up!
echo.
echo 📋 Access Points:
echo    • Backend API: http://localhost:8000
echo    • API Documentation: http://localhost:8000/docs
echo    • Flutter Web App: http://localhost:8080 (opens automatically)
echo.
echo 💡 Both services are running in separate windows
echo    Close the windows or press Ctrl+C in each to stop
echo.

pause
