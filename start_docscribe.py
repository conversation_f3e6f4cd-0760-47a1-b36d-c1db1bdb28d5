#!/usr/bin/env python3
"""
DocScribe Application Startup Script

This script helps you start the complete DocScribe application:
- Backend Python server
- Flutter app (if Flutter is available)
- Environment validation
- Service health checks
"""

import os
import sys
import subprocess
import time
import requests
import argparse
from pathlib import Path

def print_banner():
    """Print DocScribe banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                        DocScribe                             ║
    ║                                                              ║
    ║           Medical Consultation Transcription System          ║
    ║                                                              ║
    ║              🏥 NVIDIA Riva ASR Integration                  ║
    ║              🎤 Real-time Audio Processing                   ║
    ║              👥 Speaker Diarization                          ║
    ║              📄 Professional PDF Reports                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_environment():
    """Check if environment is properly configured"""
    print("\n🔍 Checking environment configuration...")
    
    # Check if backend directory exists
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Check if .env file exists
    env_file = backend_dir / ".env"
    if not env_file.exists():
        print("⚠️  .env file not found in backend directory")
        print("   Creating from .env.example...")
        
        env_example = backend_dir / ".env.example"
        if env_example.exists():
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ Created .env file from template")
            print("⚠️  Please edit backend/.env and add your NVIDIA Riva API key")
        else:
            print("❌ .env.example file not found")
            return False
    
    # Check for required environment variables
    try:
        import dotenv
        dotenv.load_dotenv(env_file)
        
        riva_api_key = os.getenv("RIVA_API_KEY")
        if not riva_api_key or riva_api_key == "your_nvidia_riva_api_key_here":
            print("⚠️  NVIDIA Riva API key not configured")
            print("   Please edit backend/.env and add your API key")
            print("   Get your API key from: https://catalog.ngc.nvidia.com/")
        else:
            print("✅ NVIDIA Riva API key configured")
            
    except ImportError:
        print("⚠️  python-dotenv not installed, skipping env validation")
    
    print("✅ Environment check completed")
    return True

def install_backend_dependencies():
    """Install Python backend dependencies"""
    print("\n📦 Installing backend dependencies...")
    
    backend_dir = Path("backend")
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found in backend directory")
        return False
    
    try:
        # Check if virtual environment exists
        venv_dir = backend_dir / "venv"
        if not venv_dir.exists():
            print("Creating virtual environment...")
            subprocess.run([
                sys.executable, "-m", "venv", str(venv_dir)
            ], check=True, cwd=backend_dir)
        
        # Determine pip path
        if os.name == 'nt':  # Windows
            pip_path = venv_dir / "Scripts" / "pip.exe"
            python_path = venv_dir / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            pip_path = venv_dir / "bin" / "pip"
            python_path = venv_dir / "bin" / "python"
        
        # Install dependencies
        print("Installing Python packages...")
        subprocess.run([
            str(python_path), "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, cwd=backend_dir)
        
        print("✅ Backend dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def start_backend_server():
    """Start the Python backend server"""
    print("\n🚀 Starting backend server...")
    
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    # Determine uvicorn path
    if os.name == 'nt':  # Windows
        uvicorn_path = venv_dir / "Scripts" / "uvicorn.exe"
        python_path = venv_dir / "Scripts" / "python.exe"
    else:  # Unix/Linux/macOS
        uvicorn_path = venv_dir / "bin" / "uvicorn"
        python_path = venv_dir / "bin" / "python"
    
    try:
        # Start uvicorn server - use python -m uvicorn for better compatibility
        cmd = [
            str(python_path),
            "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Wait for server to start
        print("Waiting for server to start...")
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get("http://localhost:8000/health", timeout=1)
                if response.status_code == 200:
                    print("✅ Backend server started successfully")
                    print("🌐 API available at: http://localhost:8000")
                    print("📚 API docs available at: http://localhost:8000/docs")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            print(f"   Waiting... ({i+1}/30)")
        
        print("❌ Backend server failed to start within 30 seconds")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ Failed to start backend server: {e}")
        return None

def check_flutter():
    """Check if Flutter is available"""
    try:
        result = subprocess.run(
            ["flutter", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ {version_line}")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️  Flutter not found or not in PATH")
    print("   Install Flutter from: https://flutter.dev/docs/get-started/install")
    return False

def start_flutter_app():
    """Start the Flutter application"""
    print("\n📱 Starting Flutter app...")
    
    flutter_dir = Path("flutter_app")
    if not flutter_dir.exists():
        print("❌ Flutter app directory not found")
        return None
    
    try:
        # Get dependencies
        print("Getting Flutter dependencies...")
        subprocess.run(
            ["flutter", "pub", "get"],
            check=True,
            cwd=flutter_dir
        )
        
        # Generate code
        print("Generating model files...")
        subprocess.run([
            "flutter", "packages", "pub", "run", "build_runner", "build"
        ], cwd=flutter_dir, capture_output=True)
        
        # Start Flutter app
        print("Starting Flutter app...")
        process = subprocess.Popen(
            ["flutter", "run"],
            cwd=flutter_dir
        )
        
        print("✅ Flutter app started")
        return process
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Flutter app: {e}")
        return None

def main():
    """Main startup function"""
    parser = argparse.ArgumentParser(description="Start DocScribe application")
    parser.add_argument("--backend-only", action="store_true", 
                       help="Start only the backend server")
    parser.add_argument("--no-install", action="store_true",
                       help="Skip dependency installation")
    parser.add_argument("--no-env-check", action="store_true",
                       help="Skip environment validation")
    
    args = parser.parse_args()
    
    print_banner()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check environment
    if not args.no_env_check and not check_environment():
        print("\n❌ Environment check failed")
        sys.exit(1)
    
    # Install dependencies
    if not args.no_install:
        if not install_backend_dependencies():
            print("\n❌ Failed to install backend dependencies")
            sys.exit(1)
    
    # Start backend server
    backend_process = start_backend_server()
    if not backend_process:
        print("\n❌ Failed to start backend server")
        sys.exit(1)
    
    # Start Flutter app (unless backend-only mode)
    flutter_process = None
    if not args.backend_only:
        if check_flutter():
            flutter_process = start_flutter_app()
        else:
            print("\n⚠️  Flutter not available, starting backend only")
    
    # Keep processes running
    try:
        print("\n🎉 DocScribe is running!")
        print("\n📋 Services:")
        print("   • Backend API: http://localhost:8000")
        print("   • API Documentation: http://localhost:8000/docs")
        if flutter_process:
            print("   • Flutter App: Running on connected device/emulator")
        
        print("\n⌨️  Press Ctrl+C to stop all services")
        
        # Wait for processes
        while True:
            if backend_process.poll() is not None:
                print("\n❌ Backend server stopped unexpectedly")
                break
            
            if flutter_process and flutter_process.poll() is not None:
                print("\n⚠️  Flutter app stopped")
                flutter_process = None
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping DocScribe...")
        
        if backend_process:
            backend_process.terminate()
            print("   • Backend server stopped")
        
        if flutter_process:
            flutter_process.terminate()
            print("   • Flutter app stopped")
        
        print("\n👋 DocScribe stopped successfully")

if __name__ == "__main__":
    main()
