@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                   DocScribe Startup (Windows)                ║
echo ║                                                              ║
echo ║              Medical Transcription System                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting DocScribe Backend Server...
echo.

REM Check if backend directory exists
if not exist "backend" (
    echo ❌ Backend directory not found!
    echo    Make sure you're in the DocScribe project directory
    echo    Run setup_windows.bat first
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "backend\venv" (
    echo ❌ Virtual environment not found!
    echo    Run setup_windows.bat first to set up the environment
    pause
    exit /b 1
)

REM Navigate to backend directory
cd backend

REM Check if .env file exists
if not exist ".env" (
    echo ❌ Environment file not found!
    echo    Run setup_windows.bat first to create .env file
    pause
    exit /b 1
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Start the server
echo 🌐 Starting FastAPI server...
echo    Backend API: http://localhost:8000
echo    API Docs: http://localhost:8000/docs
echo    Health Check: http://localhost:8000/health
echo.
echo 💡 Press Ctrl+C to stop the server
echo.

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

echo.
echo 👋 DocScribe server stopped
pause
