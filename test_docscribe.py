#!/usr/bin/env python3
"""
DocScribe Testing Script

This script runs comprehensive tests for the DocScribe application:
- Backend API tests
- NVIDIA Riva integration tests
- Database functionality tests
- PDF generation tests
- Audio processing tests
"""

import asyncio
import json
import requests
import time
import tempfile
import wave
import numpy as np
from pathlib import Path
import sys

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30

def print_test_banner():
    """Print test banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                   DocScribe Tests                            ║
    ║                                                              ║
    ║              Comprehensive System Testing                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def wait_for_server():
    """Wait for backend server to be available"""
    print("🔍 Waiting for backend server...")
    
    for i in range(30):
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Backend server is ready")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(f"   Waiting... ({i+1}/30)")
    
    print("❌ Backend server not available")
    return False

def test_health_endpoint():
    """Test health check endpoint"""
    print("\n🏥 Testing health endpoint...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {data.get('status')}")
            print(f"   Environment: {data.get('environment')}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_api_documentation():
    """Test API documentation endpoint"""
    print("\n📚 Testing API documentation...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        
        if response.status_code == 200:
            print("✅ API documentation accessible")
            return True
        else:
            print(f"⚠️  API documentation: {response.status_code}")
            return True  # Non-critical
            
    except Exception as e:
        print(f"⚠️  API documentation error: {e}")
        return True  # Non-critical

def test_consultation_workflow():
    """Test complete consultation workflow"""
    print("\n👥 Testing consultation workflow...")
    
    try:
        # 1. Start consultation
        print("   Starting consultation...")
        consultation_data = {
            "patient_name": "John Doe",
            "age": 45,
            "gender": "Male",
            "patient_id": "TEST001",
            "visit_reason": "Annual checkup",
            "doctor_name": "Dr. Smith",
            "language_code": "en-US"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/start_consultation",
            json=consultation_data,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to start consultation: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        consultation_id = response.json()["consultation_id"]
        print(f"✅ Consultation started: {consultation_id}")
        
        # 2. Send mock audio chunk
        print("   Sending audio chunk...")
        audio_data = generate_mock_audio()
        
        files = {"audio": ("test.wav", audio_data, "audio/wav")}
        headers = {"X-Consultation-ID": consultation_id}
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/audio_chunk",
            files=files,
            headers=headers,
            timeout=15
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to send audio chunk: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        chunk_response = response.json()
        print(f"✅ Audio chunk processed: {chunk_response['status']}")
        
        # 3. Finish consultation
        print("   Finishing consultation...")
        response = requests.post(
            f"{API_BASE_URL}/api/v1/finish_consultation",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to finish consultation: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        finish_response = response.json()
        transcript = finish_response.get("clean_transcript", [])
        print(f"✅ Consultation finished with {len(transcript)} transcript segments")
        
        # 4. Get consultation details
        print("   Retrieving consultation...")
        response = requests.get(
            f"{API_BASE_URL}/api/v1/consultations/{consultation_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            consultation = response.json()
            print(f"✅ Consultation retrieved: {consultation['status']}")
        else:
            print(f"⚠️  Failed to retrieve consultation: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Consultation workflow error: {e}")
        return False

def test_transcript_endpoints():
    """Test transcript management endpoints"""
    print("\n📝 Testing transcript endpoints...")
    
    try:
        # Get consultations list
        response = requests.get(f"{API_BASE_URL}/api/v1/consultations", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to get consultations: {response.status_code}")
            return False
        
        consultations = response.json()["consultations"]
        if not consultations:
            print("⚠️  No consultations found for transcript testing")
            return True
        
        consultation_id = consultations[0]["id"]
        print(f"✅ Found consultation for testing: {consultation_id}")
        
        # Test transcript retrieval
        response = requests.get(
            f"{API_BASE_URL}/api/v1/consultations/{consultation_id}/transcript",
            timeout=10
        )
        
        if response.status_code == 200:
            transcript = response.json()
            print(f"✅ Transcript retrieved: {len(transcript)} segments")
        else:
            print(f"⚠️  Transcript retrieval: {response.status_code}")
        
        # Test transcript statistics
        response = requests.get(
            f"{API_BASE_URL}/api/v1/consultations/{consultation_id}/transcript/statistics",
            timeout=10
        )
        
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Transcript statistics: {stats.get('total_words', 0)} words")
        else:
            print(f"⚠️  Transcript statistics: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transcript endpoints error: {e}")
        return False

def test_pdf_generation():
    """Test PDF generation"""
    print("\n📄 Testing PDF generation...")
    
    try:
        # Get consultations list
        response = requests.get(f"{API_BASE_URL}/api/v1/consultations", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to get consultations: {response.status_code}")
            return False
        
        consultations = response.json()["consultations"]
        if not consultations:
            print("⚠️  No consultations found for PDF testing")
            return True
        
        consultation_id = consultations[0]["id"]
        
        # Generate PDF
        pdf_data = {
            "doctor_notes": "Test notes for PDF generation. Patient appears healthy."
        }
        headers = {"X-Consultation-ID": consultation_id}
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/generate_pdf",
            json=pdf_data,
            headers=headers,
            timeout=20
        )
        
        if response.status_code == 200:
            pdf_response = response.json()
            print(f"✅ PDF generated: {pdf_response['pdf_url']}")
            
            # Test PDF download
            download_response = requests.get(
                f"{API_BASE_URL}{pdf_response['pdf_url']}",
                timeout=10
            )
            
            if download_response.status_code == 200:
                print(f"✅ PDF download successful: {len(download_response.content)} bytes")
            else:
                print(f"⚠️  PDF download failed: {download_response.status_code}")
            
        else:
            print(f"❌ PDF generation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PDF generation error: {e}")
        return False

def generate_mock_audio():
    """Generate mock audio data for testing"""
    # Generate 1 second of sine wave audio (440 Hz)
    sample_rate = 16000
    duration = 1.0
    frequency = 440
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit PCM
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Create WAV file in memory
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
        with wave.open(temp_file.name, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        # Read the file back
        with open(temp_file.name, 'rb') as f:
            return f.read()

def test_error_handling():
    """Test error handling"""
    print("\n🚨 Testing error handling...")
    
    try:
        # Test invalid consultation ID
        response = requests.get(
            f"{API_BASE_URL}/api/v1/consultations/invalid-id",
            timeout=5
        )
        
        if response.status_code == 404:
            print("✅ Invalid consultation ID handled correctly")
        else:
            print(f"⚠️  Unexpected response for invalid ID: {response.status_code}")
        
        # Test missing headers
        response = requests.post(
            f"{API_BASE_URL}/api/v1/audio_chunk",
            files={"audio": ("test.wav", b"invalid", "audio/wav")},
            timeout=5
        )
        
        if response.status_code in [400, 422]:
            print("✅ Missing headers handled correctly")
        else:
            print(f"⚠️  Unexpected response for missing headers: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test error: {e}")
        return False

def run_performance_test():
    """Run basic performance test"""
    print("\n⚡ Running performance test...")
    
    try:
        # Test multiple rapid requests
        start_time = time.time()
        successful_requests = 0
        
        for i in range(10):
            try:
                response = requests.get(f"{API_BASE_URL}/health", timeout=2)
                if response.status_code == 200:
                    successful_requests += 1
            except:
                pass
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ Performance test completed")
        print(f"   Successful requests: {successful_requests}/10")
        print(f"   Total time: {duration:.2f} seconds")
        print(f"   Average response time: {duration/10:.3f} seconds")
        
        return successful_requests >= 8  # Allow some failures
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

def main():
    """Main test function"""
    print_test_banner()
    
    # Check if server is running
    if not wait_for_server():
        print("\n❌ Backend server not available")
        print("   Start the server with: python start_docscribe.py --backend-only")
        sys.exit(1)
    
    # Run tests
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("API Documentation", test_api_documentation),
        ("Consultation Workflow", test_consultation_workflow),
        ("Transcript Endpoints", test_transcript_endpoints),
        ("PDF Generation", test_pdf_generation),
        ("Error Handling", test_error_handling),
        ("Performance", run_performance_test),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! DocScribe is working correctly.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
