#!/usr/bin/env python3
"""
Simple DocScribe Test Script

Basic test to verify the backend is working without complex dependencies.
"""

import requests
import time
import json

API_BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🏥 Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data.get('status')}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_basic_consultation():
    """Test basic consultation workflow"""
    print("\n👥 Testing basic consultation...")
    try:
        # Start consultation
        consultation_data = {
            "patient_name": "Test Patient",
            "age": 30,
            "gender": "Male",
            "doctor_name": "Dr. Test",
            "language_code": "en-US"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/start_consultation",
            json=consultation_data,
            timeout=10
        )
        
        if response.status_code == 200:
            consultation_id = response.json()["consultation_id"]
            print(f"✅ Consultation started: {consultation_id}")
            return True
        else:
            print(f"❌ Failed to start consultation: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Consultation test error: {e}")
        return False

def test_api_docs():
    """Test API documentation"""
    print("\n📚 Testing API documentation...")
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API documentation accessible")
            return True
        else:
            print(f"⚠️  API documentation: {response.status_code}")
            return True  # Non-critical
    except Exception as e:
        print(f"⚠️  API documentation error: {e}")
        return True  # Non-critical

def wait_for_server():
    """Wait for server to be available"""
    print("🔍 Waiting for backend server...")
    for i in range(10):
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Backend server is ready")
                return True
        except:
            pass
        time.sleep(1)
        print(f"   Waiting... ({i+1}/10)")
    
    print("❌ Backend server not available")
    return False

def main():
    """Main test function"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                                                              ║")
    print("║                   DocScribe Simple Test                      ║")
    print("║                                                              ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    if not wait_for_server():
        print("\n❌ Backend server not available")
        print("   Start the server with: python -m uvicorn app.main:app --reload")
        return False
    
    tests = [
        ("Health Check", test_health),
        ("API Documentation", test_api_docs),
        ("Basic Consultation", test_basic_consultation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! DocScribe backend is working.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
