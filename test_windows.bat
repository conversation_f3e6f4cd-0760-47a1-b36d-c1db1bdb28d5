@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                   DocScribe Tests (Windows)                  ║
echo ║                                                              ║
echo ║              Comprehensive System Testing                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🧪 Running DocScribe tests...
echo.

REM Check if backend is running
echo 🔍 Checking if backend server is running...
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend server is not running!
    echo    Start the server first with: start_windows.bat
    echo    Or run: python start_docscribe.py --backend-only
    pause
    exit /b 1
)

echo ✅ Backend server is running
echo.

REM Run Python tests
echo 🐍 Running Python test suite...
python test_docscribe.py

echo.
echo 📋 Test completed!
echo    Check the output above for any failures
echo.

pause
